---
import FormLayout from '../forms/FormLayout.astro';
import FormField from '../forms/FormField.astro';

const ageGroups = [
  { value: "u6", label: "U6 (Unter 6 Jahre)" },
  { value: "u7", label: "U7 (Unter 7 Jahre)" },
  { value: "u8", label: "U8 (Unter 8 Jahre)" },
  { value: "u9", label: "U9 (Unter 9 Jahre)" },
  { value: "u10", label: "U10 (Unter 10 Jahre)" },
  { value: "u11", label: "U11 (Unter 11 Jahre)" },
  { value: "u12", label: "U12 (Unter 12 Jahre)" },
  { value: "u13", label: "U13 (Unter 13 Jahre)" },
  { value: "u14", label: "U14 (Unter 14 Jahre)" },
  { value: "u15", label: "U15 (Unter 15 Jahre)" },
  { value: "u16", label: "U16 (Unter 16 Jahre)" },
  { value: "u18", label: "U18 (Unter 18 Jahre)" },
  { value: "adult", label: "Erwachsene" }
];
---
      <FormLayout 
        title="Schnuppertraining Anmeldeformular" 
        formId="schnuppertraining-form"
        submitText="Anmeldung absenden"
        successMessage="Vielen Dank für Ihre Anmeldung! Wir werden uns in Kürze bei Ihnen melden, um einen Termin für das Schnuppertraining zu vereinbaren."
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-bold mb-4 text-usc-primary">Daten des Teilnehmers/der Teilnehmerin</h3>
            
            <FormField 
              label="Vorname" 
              name="participant_first_name" 
              required={true} 
            />
            
            <FormField 
              label="Nachname" 
              name="participant_last_name" 
              required={true} 
            />
            
            <FormField 
              label="Geburtsdatum" 
              type="date" 
              name="participant_birthdate" 
              required={true} 
            />
            
            <FormField 
              label="Altersgruppe" 
              type="select" 
              name="age_group" 
              required={true}
              options={ageGroups}
            />
            
            <FormField 
              label="Bisherige Fußballerfahrung" 
              type="select" 
              name="experience" 
              required={true}
              options={[
                { value: "none", label: "Keine Erfahrung" },
                { value: "hobby", label: "Hobbymäßig (Schule, Freizeit)" },
                { value: "club", label: "Vereinserfahrung" }
              ]}
            />
            
            <FormField 
              label="Bisheriger Verein (falls vorhanden)" 
              name="previous_club" 
            />
          </div>
          
          <div>
            <h3 class="text-lg font-bold mb-4 text-usc-primary">Kontaktdaten</h3>
            
            <FormField 
              label="Ansprechperson (bei Minderjährigen)" 
              name="contact_person" 
            />
            
            <FormField 
              label="E-Mail" 
              type="email" 
              name="email" 
              required={true} 
            />
            
            <FormField 
              label="Telefon" 
              type="tel" 
              name="phone" 
              required={true} 
            />
            
            <FormField 
              label="Adresse" 
              name="address" 
              required={true} 
            />
            
            <FormField 
              label="PLZ" 
              name="postal_code" 
              required={true} 
            />
            
            <FormField 
              label="Ort" 
              name="city" 
              required={true} 
            />
          </div>
        </div>
        
        <div class="mt-6">
          <h3 class="text-lg font-bold mb-4 text-usc-primary">Terminwünsche</h3>
          
          <FormField 
            label="Bevorzugte Wochentage" 
            type="checkbox" 
            name="preferred_days_monday" 
            label="Montag"
          />
          
          <FormField 
            type="checkbox" 
            name="preferred_days_tuesday" 
            label="Dienstag"
          />
          
          <FormField 
            type="checkbox" 
            name="preferred_days_wednesday" 
            label="Mittwoch"
          />
          
          <FormField 
            type="checkbox" 
            name="preferred_days_thursday" 
            label="Donnerstag"
          />
          
          <FormField 
            type="checkbox" 
            name="preferred_days_friday" 
            label="Freitag"
          />
          
          <FormField 
            label="Anmerkungen zum Termin" 
            type="textarea" 
            name="date_notes" 
            placeholder="z.B. bevorzugte Uhrzeiten, Zeitraum, etc."
          />
        </div>
        
        <div class="mt-6">
          <h3 class="text-lg font-bold mb-4 text-usc-primary">Zusätzliche Informationen</h3>
          
          <FormField 
            label="Sonstige Anmerkungen" 
            type="textarea" 
            name="notes" 
          />
          
          <FormField 
            type="checkbox" 
            name="privacy_consent" 
            label="Ich habe die Datenschutzerklärung gelesen und stimme der Verarbeitung meiner Daten zu."
            required={true}
          >
            <p class="text-sm text-gray-500">
              Die von Ihnen angegebenen Daten werden ausschließlich zum Zweck der Anmeldung und für vereinsinterne Kommunikation verwendet.
              Eine Weitergabe an Dritte erfolgt nicht. Sie können Ihre Einwilligung jederzeit widerrufen.
            </p>
          </FormField>
        </div>
      </FormLayout>
