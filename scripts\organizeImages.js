import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { existsSync } from 'fs';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function organizeImages() {
  try {
    // Read the metadata file
    const metadataPath = path.join(__dirname, 'image_metadata.json');
    const metadataContent = await fs.readFile(metadataPath, 'utf-8');

    // Parse the metadata (removing the trailing comma if present)
    const cleanedContent = metadataContent.replace(/,\s*\{\}\s*\]$/, '\n]').replace(/,\s*$/, '\n]');
    const metadata = JSON.parse(cleanedContent);

    console.log(`Loaded metadata for ${metadata.length} images`);

    // Group images by category
    const imagesByCategory = {};
    for (const img of metadata) {
      const category = img.category || 'images/static';
      if (!imagesByCategory[category]) {
        imagesByCategory[category] = [];
      }
      imagesByCategory[category].push(img);
    }

    // Print summary
    console.log('\nImage Distribution:');
    for (const [category, images] of Object.entries(imagesByCategory)) {
      console.log(`${category}: ${images.length} images`);
    }

    // Create a report file with image details
    let report = '# USC Perchtoldsdorf Image Assets Report\n\n';

    for (const [category, images] of Object.entries(imagesByCategory)) {
      report += `## ${category} (${images.length} images)\n\n`;

      for (const img of images) {
        report += `- **${img.filename}**\n`;
        report += `  - Original URL: ${img.originalSrc}\n`;
        report += `  - Alt Text: ${img.alt || 'None'}\n`;
        report += `  - Size: ${img.width || '?'}x${img.height || '?'}\n`;
        report += `  - Found on: ${img.pageUrl}\n`;
        report += `  - Local Path: ${img.localPath}\n\n`;
      }
    }

    // Save the report
    const reportPath = path.join(__dirname, 'image_report.md');
    await fs.writeFile(reportPath, report);
    console.log(`\nImage report saved to ${reportPath}`);

  } catch (error) {
    console.error('Error organizing images:', error);
  }
}

// Run the organizer
organizeImages().catch(console.error);
