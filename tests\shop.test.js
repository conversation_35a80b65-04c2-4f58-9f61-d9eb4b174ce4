/**
 * Playwright test for shop page functionality
 *
 * This test verifies that the shop page works correctly, including:
 * - Loading and displaying content from the markdown file
 * - Featured products section
 * - Category filter widgets
 * - Product cards with hover effects
 * - Newsletter signup widget
 * - Social proof elements (testimonials)
 * - Responsive design
 */

import { test, expect } from '@playwright/test';

test.describe('Shop Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the shop page
    await page.goto('/shop');
    await page.waitForLoadState('networkidle');
  });

  test('should load and display the shop page with hero section', async ({ page }) => {
    // Check that the page title is correct
    await expect(page).toHaveTitle(/Shop - USC Perchtoldsdorf/);

    // Check that the hero section is displayed
    const heroSection = page.locator('.relative.bg-gray-900.text-white');
    await expect(heroSection).toBeVisible();

    // Check that the hero title is displayed
    const heroTitle = heroSection.locator('h1');
    await expect(heroTitle).toBeVisible();
    await expect(heroTitle).toContainText('USC Perchtoldsdorf');
  });

  test('should display featured products section', async ({ page }) => {
    // Check that the featured products section is displayed
    const featuredSection = page.locator('section', { hasText: 'Unsere Empfehlungen' });
    await expect(featuredSection).toBeVisible();

    // Check that there are featured products
    const featuredProducts = featuredSection.locator('.grid > div');
    await expect(featuredProducts).toHaveCount(3);

    // Check that each featured product has an image, title, price, and button
    for (let i = 0; i < 3; i++) {
      const product = featuredProducts.nth(i);
      await expect(product.locator('img')).toBeVisible();
      await expect(product.locator('h3')).toBeVisible();
      await expect(product.locator('p.text-lg.font-bold')).toBeVisible();
      await expect(product.locator('a', { hasText: 'Details ansehen' })).toBeVisible();
    }
  });

  test('should display markdown content from shop.md', async ({ page }) => {
    // Check that the markdown content section is displayed
    const contentSection = page.locator('section.prose.prose-lg');
    
    // The content might not be visible if the markdown file is not found
    // So we'll check if it exists first
    const contentExists = await contentSection.count() > 0;
    
    if (contentExists) {
      await expect(contentSection).toBeVisible();
      
      // Check for specific content from the markdown file
      const contentText = await contentSection.textContent();
      expect(contentText).toContain('Onlineshops');
    }
  });

  test('should have working category filters', async ({ page }) => {
    // Check that the category filters are displayed
    const categoryFilters = page.locator('ul li a', { hasText: 'Trikots' });
    await expect(categoryFilters).toBeVisible();

    // Click on a category filter
    await categoryFilters.click();
    
    // Wait for navigation to complete
    await page.waitForURL('**/shop/kategorie/trikots');
    
    // Check that the page has loaded with the filtered products
    const pageTitle = page.locator('h1');
    await expect(pageTitle).toBeVisible();
  });

  test('should have working sort functionality', async ({ page }) => {
    // Check that the sort select is displayed
    const sortSelect = page.locator('#sort-select');
    await expect(sortSelect).toBeVisible();

    // Select a sort option
    await sortSelect.selectOption('price-asc');
    
    // Wait for navigation to complete
    await page.waitForURL('**/shop?sort=price-asc');
    
    // Check that the page has loaded with the sorted products
    const pageTitle = page.locator('h1');
    await expect(pageTitle).toBeVisible();
  });

  test('should display testimonials section', async ({ page }) => {
    // Check that the testimonials section is displayed
    const testimonialsSection = page.locator('div', { hasText: 'Kundenmeinungen' });
    await expect(testimonialsSection).toBeVisible();

    // Check that there are testimonials
    const testimonials = testimonialsSection.locator('div.p-6 > div');
    await expect(testimonials).toHaveCount(3);

    // Check that each testimonial has a rating, text, and name
    for (let i = 0; i < 3; i++) {
      const testimonial = testimonials.nth(i);
      await expect(testimonial.locator('svg')).toBeVisible();
      await expect(testimonial.locator('p')).toBeVisible();
      await expect(testimonial.locator('.flex.justify-between')).toBeVisible();
    }
  });

  test('should display newsletter signup widget', async ({ page }) => {
    // Check that the newsletter signup widget is displayed
    const newsletterWidget = page.locator('div', { hasText: 'Newsletter' });
    await expect(newsletterWidget).toBeVisible();

    // Check that the newsletter form is displayed
    const newsletterForm = newsletterWidget.locator('form');
    await expect(newsletterForm).toBeVisible();

    // Check that the form has an email input and a submit button
    await expect(newsletterForm.locator('input[type="email"]')).toBeVisible();
    await expect(newsletterForm.locator('button[type="submit"]')).toBeVisible();
  });

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 });

    // Check that the page layout adjusts for mobile
    const mainContent = page.locator('.container.mx-auto');
    await expect(mainContent).toBeVisible();

    // Check that the sidebar is below the product grid on mobile
    const sidebar = page.locator('.w-full.lg\\:w-1\\/4.order-2');
    const productGrid = page.locator('.w-full.lg\\:w-3\\/4.order-1');
    
    // Get the bounding boxes to check positioning
    const sidebarBox = await sidebar.boundingBox();
    const productGridBox = await productGrid.boundingBox();
    
    // On mobile, the sidebar should be below the product grid
    expect(sidebarBox.y).toBeGreaterThan(productGridBox.y);
  });
});
