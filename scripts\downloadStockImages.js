import { chromium } from 'playwright';
import fs from 'fs/promises';
import { createWriteStream, existsSync } from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';
import { createHash } from 'crypto';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// List of free stock image URLs that match the USC Perchtoldsdorf theme
// These are pre-selected high-quality, free-to-use images related to soccer/football, sports, and community
const stockImages = [
  // Hero images (soccer fields, stadium, action shots)
  {
    url: 'https://images.unsplash.com/photo-1540379708242-14a809bef941',
    category: 'hero',
    name: 'soccer-field-aerial',
    description: 'Aerial view of a soccer field'
  },
  {
    url: 'https://images.unsplash.com/photo-1459865264687-595d652de67e',
    category: 'hero',
    name: 'soccer-stadium-crowd',
    description: 'Soccer stadium with crowd'
  },
  {
    url: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018',
    category: 'hero',
    name: 'soccer-ball-field',
    description: 'Soccer ball on field at sunset'
  },
  {
    url: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d',
    category: 'hero',
    name: 'soccer-action',
    description: 'Soccer action shot'
  },
  {
    url: 'https://images.unsplash.com/photo-1517466787929-bc90951d0974',
    category: 'hero',
    name: 'soccer-field-sunset',
    description: 'Soccer field at sunset'
  },

  // Background images (textures, blurred sports scenes)
  {
    url: 'https://images.unsplash.com/photo-1556056504-5c7696c4c28d',
    category: 'backgrounds',
    name: 'soccer-grass-texture',
    description: 'Soccer field grass texture'
  },
  {
    url: 'https://images.unsplash.com/photo-1511426463457-0571e247d816',
    category: 'backgrounds',
    name: 'soccer-net-closeup',
    description: 'Close-up of soccer net'
  },
  {
    url: 'https://images.unsplash.com/photo-1521731978332-9e9e714bdd20',
    category: 'backgrounds',
    name: 'soccer-ball-closeup',
    description: 'Close-up of soccer ball'
  },
  {
    url: 'https://images.unsplash.com/photo-1553778263-73a83bab9b0c',
    category: 'backgrounds',
    name: 'soccer-field-lines',
    description: 'Soccer field lines'
  },
  {
    url: 'https://images.unsplash.com/photo-1550881111-7cfde14b8073',
    category: 'backgrounds',
    name: 'soccer-field-evening',
    description: 'Soccer field in the evening'
  },

  // Pattern images (subtle textures, repeating patterns)
  {
    url: 'https://images.unsplash.com/photo-1552667466-07770ae110d0',
    category: 'patterns',
    name: 'soccer-ball-pattern',
    description: 'Soccer ball pattern'
  },
  {
    url: 'https://images.unsplash.com/photo-1544914379-806667cd9489',
    category: 'patterns',
    name: 'soccer-net-pattern',
    description: 'Soccer net pattern'
  },
  {
    url: 'https://images.unsplash.com/photo-1551958219-acbc608c6377',
    category: 'patterns',
    name: 'soccer-field-pattern',
    description: 'Soccer field pattern'
  },
  {
    url: 'https://images.unsplash.com/photo-1577223625816-7546f13df25d',
    category: 'patterns',
    name: 'soccer-jerseys-pattern',
    description: 'Soccer jerseys pattern'
  },
  {
    url: 'https://images.unsplash.com/photo-1560272564-c83b66b1ad12',
    category: 'patterns',
    name: 'soccer-crowd-pattern',
    description: 'Soccer crowd pattern'
  }
];

async function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    // Add query parameters to get a high-quality version of the image
    const fullUrl = `${url}?q=85&w=1920&auto=format&fit=crop`;

    https.get(fullUrl, (res) => {
      if (res.statusCode === 200) {
        res.pipe(createWriteStream(filepath))
           .on('error', reject)
           .once('close', () => resolve(filepath));
      } else {
        res.resume();
        reject(new Error(`Request Failed With a Status Code: ${res.statusCode}`));
      }
    }).on('error', (err) => {
      reject(new Error(`Failed to download: ${err.message}`));
    });
  });
}

async function downloadStockImages() {
  // Create directories if they don't exist
  const baseDir = path.join(__dirname, '..', 'public', 'images');
  const categories = ['hero', 'backgrounds', 'patterns'];

  for (const category of categories) {
    await fs.mkdir(path.join(baseDir, category), { recursive: true });
  }

  // Initialize metadata file
  const metadataPath = path.join(__dirname, 'stock_image_metadata.json');
  await fs.writeFile(metadataPath, '[\n');

  let totalDownloaded = 0;

  // Download each stock image
  for (const [index, img] of stockImages.entries()) {
    try {
      const { url, category, name, description } = img;

      // Create filename with the provided name
      const filename = `${name}.jpg`;
      const filepath = path.join(baseDir, category, filename);

      // Download the image
      await downloadImage(url, filepath);
      totalDownloaded++;
      console.log(`Downloaded: ${filename} to ${category}`);

      // Save metadata for later reference
      const metadata = {
        originalUrl: url,
        category,
        name,
        description,
        filename,
        localPath: path.join('images', category, filename).replace(/\\/g, '/'),
      };

      await fs.appendFile(
        metadataPath,
        JSON.stringify(metadata, null, 2) + (index < stockImages.length - 1 ? ',\n' : '\n')
      );
    } catch (error) {
      console.error(`Failed to download image ${img.url}:`, error.message);
    }
  }

  // Close the metadata file
  await fs.appendFile(metadataPath, ']');

  console.log(`Stock image download completed! Downloaded ${totalDownloaded} images.`);

  // Generate a markdown report
  let report = '# USC Perchtoldsdorf Stock Images\n\n';
  report += 'These free-to-use stock images have been selected to complement the USC Perchtoldsdorf website design.\n\n';

  for (const category of categories) {
    const categoryImages = stockImages.filter(img => img.category === category);
    report += `## ${category.charAt(0).toUpperCase() + category.slice(1)} Images (${categoryImages.length})\n\n`;

    for (const img of categoryImages) {
      report += `### ${img.name}\n`;
      report += `- **Description**: ${img.description}\n`;
      report += `- **Source**: [Unsplash](${img.url})\n`;
      report += `- **Local Path**: \`/uploads/images/${img.category}/${img.name}.jpg\`\n\n`;
    }
  }

  // Save the report
  const reportPath = path.join(__dirname, 'stock_image_report.md');
  await fs.writeFile(reportPath, report);
  console.log(`Stock image report saved to ${reportPath}`);
}

// Run the downloader
downloadStockImages().catch(console.error);
