---
title: "Email Integration Test"
description: "Hier findest du eine detaillierte Anleitung, um die Email-Integration auf der USC Perchtoldsdorf-Website zu testen."
category: "Dev"
order: 10
---

# Email Integration Testing Guide (Deutsch)
Diese Datei enthält eine deutsche Kurzfassung der wichtigsten Punkte.

---

This document provides instructions for testing the email integration functionality on the USC Perchtoldsdorf website.

## Prerequisites

Before testing, make sure you have:
1. Deployed the site to Netlify with form handling enabled
2. Access to the email inbox used for Netlify form notifications

## Testing the Contact Form

1. **Navigate to the Contact Page**
   - Go to the website and click on "Kontakt" in the navigation menu
   - Verify that the contact form is displayed

2. **Test Form Validation**
   - Try submitting the form without filling in any fields
   - Verify that validation errors are displayed
   - Try entering an invalid email address
   - Verify that an email validation error is displayed

3. **Submit a Valid Form**
   - Fill in all required fields with valid data:
     - Name: Test User
     - Email: <EMAIL>
     - Subject: Test Contact Form
     - Message: This is a test message to verify that the contact form is working correctly.
     - Check the privacy consent checkbox
   - Click the "Submit" button
   - Verify that a success message is displayed

4. **Check Email Delivery**
   - Check the email inbox associated with your Netlify account
   - Verify that you received an email with the form submission
   - Verify that all form data is correctly included in the email

## Testing Newsletter Subscription

1. **Test Footer Newsletter Form**
   - Scroll to the bottom of any page
   - Find the newsletter subscription form in the footer
   - Try submitting without an email address
   - Verify that validation prevents submission
   - Enter a valid email address
   - Submit the form
   - Verify that a success message is displayed

2. **Test Newsletter Component**
   - Navigate to the News page
   - Find the newsletter subscription component
   - Enter a valid email address
   - Check the privacy consent checkbox
   - Submit the form
   - Verify that a success message is displayed

3. **Check Email Delivery**
   - Check the email inbox associated with your Netlify account
   - Verify that you received an email with the subscription
   - Verify that the email address is correctly included

## Troubleshooting

If the tests fail, check the following:

1. **Netlify Setup**
   - Verify that the site is correctly deployed on Netlify

2. **Network Issues**
   - Check the browser console for any network errors
   - Verify that the API endpoints are returning the expected responses

3. **Email Delivery**
   - Check spam/junk folders if emails are not appearing in the inbox
   - Verify that the email address associated with your Netlify account is correct

## Manual Testing Checklist

Use this checklist to verify all aspects of the email integration:

- [ ] Contact form validation works correctly
- [ ] Contact form submits successfully
- [ ] Contact form emails are received
- [ ] Newsletter form validation works correctly
- [ ] Newsletter form submits successfully
- [ ] Newsletter subscription emails are received
- [ ] Success messages are displayed correctly
- [ ] Error messages are displayed correctly when appropriate
