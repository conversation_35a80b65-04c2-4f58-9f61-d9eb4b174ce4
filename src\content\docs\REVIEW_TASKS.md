---
title: "ClickUp Review Tasks"
description: "Statusübersicht der in ClickUp befindlichen Review-Aufgaben"
category: "Dev"
order: 15
---

# Review Tasks Status (Stand 2025‑06‑28)

Die folgende Tabelle gibt einen Überblick über die aus ClickUp übernommenen Tasks, die sich im Status **Review** befanden. Auf Basis des aktuellen Repos wurden sie in zwei Kategorien eingeteilt:

- **Kann abgeschlossen werden** – Aufgabe wurde technisch umgesetzt und benötigt kein weiteres Coding.
- **Benötigt Kunden-Feedback** – Inhalte oder Bilder müssen final vom Verein bestätigt werden.

| Task | Link | Empfehlung |
|------|------|------------|
| Qualitätskontrolle für migrierte Inhalte durchführen | <https://app.clickup.com/t/86c3e2bpv> | Kann abgeschlossen werden |
| Alle Content-Migrationsprobleme beheben | <https://app.clickup.com/t/86c3e2bpz> | Kann abgeschlossen werden |
| Seitentitel an die Originalseite anpassen | <https://app.clickup.com/t/86c3e2bq1> | Kann abgeschlossen werden |
| Komponenten und Seitentemplates überprüfen | <https://app.clickup.com/t/86c3e2bqg> | Kann abgeschlossen werden |
| Fehlende Produktbilder beheben | <https://app.clickup.com/t/86c3e2bq9> | Kann abgeschlossen werden |
| Sicherstellen, dass Produktbilder korrekt geladen werden | <https://app.clickup.com/t/86c3e2bqf> | Kann abgeschlossen werden |
| Sicherstellen, dass alle Originalinhalte korrekt migriert wurden | <https://app.clickup.com/t/86c3e2bq6> | Kann abgeschlossen werden |
| Prüfen, ob Bilder auf allen Seiten korrekt angezeigt werden | <https://app.clickup.com/t/86c3e2bqn> | Kann abgeschlossen werden |
| Alle Links korrekt überprüfen (intern und extern) | <https://app.clickup.com/t/86c3e2bqu> | Kann abgeschlossen werden |
| Produktkarten-Komponente so anpassen, dass Produkte korrekt angezeigt werden | <https://app.clickup.com/t/86c3e2br0> | Kann abgeschlossen werden |
| Prüfen, warum die Produktanzahl nicht angezeigt wird | <https://app.clickup.com/t/86c3e2bqt> | Kann abgeschlossen werden |
| Content-Migration für alle Sammlungen abschließen | <https://app.clickup.com/t/86c3e2bqz> | Kann abgeschlossen werden |
| Sichere Speicherung und Nutzung des Snipcart Public API Keys | <https://app.clickup.com/t/86c3e2bqq> | Benötigt Kunden-Feedback |
| Fehlerbehandlung und Logging verbessern | <https://app.clickup.com/t/86c3e2bqr> | Kann abgeschlossen werden |
| Fehlerhafte Bilder auf allen Seiten beheben | <https://app.clickup.com/t/86c3e2br4> | Kann abgeschlossen werden |
| Anzeige von Teams, Sponsoren und News-Inhalten beheben | <https://app.clickup.com/t/86c3e2bqx> | Kann abgeschlossen werden |
| Scraping-Skripte erneut ausführen und Ergebnisse überprüfen | <https://app.clickup.com/t/86c3e2br5> | Kann abgeschlossen werden |
| Prüfen, ob Inhalte korrekt aus Sammlungen geladen werden | <https://app.clickup.com/t/86c3e2bqv> | Kann abgeschlossen werden |
| WIR steht über dich – Bild als Leitgedanke einbinden | <https://app.clickup.com/t/86c3v3rqx> | Benötigt Kunden-Feedback |
| Spritzerturnier hinzufügen | <https://app.clickup.com/t/86c3v5hc8> | Kann abgeschlossen werden |
| Bilder bei Funktionären einfügen | <https://app.clickup.com/t/86c3v5rb2> | Benötigt Kunden-Feedback |
| Präsidentin einfügen (Text) | <https://app.clickup.com/t/86c3v5t56> | Kann abgeschlossen werden |
| Sportplatz – Luftbilder einfügen | <https://app.clickup.com/t/86c3v5uuv> | Kann abgeschlossen werden |
| Mannschaften laut vereine.oefb.at einpflegen | <https://app.clickup.com/t/86c3v60v0> | Benötigt Kunden-Feedback |
| Text bei News (Minigolf) korrigieren | <https://app.clickup.com/t/86c3v6jfc> | Kann abgeschlossen werden |
| favicon anpassen | <https://app.clickup.com/t/86c40htr6> | Kann abgeschlossen werden |

