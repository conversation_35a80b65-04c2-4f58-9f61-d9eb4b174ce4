import { getStore } from '@netlify/blobs';
import { getEntry } from 'astro:content';

let store;

export async function GET({ url }) {
  const eventId = url.searchParams.get('id');
  if (!eventId) return new Response('id missing', { status: 400 });

  const key = `schedule:${eventId}`;

  if (!store) {
    try {
      store = getStore('schedules');
    } catch (err) {
        throw err;
    }
  }

  let blob = null;
  if (store) {
    try {
      blob = await store.get(key, { type: 'json' });
      if (blob) {
        console.log('Blob found:', blob);
      }
    } catch (err) {
      console.error('blob get failed', err);
    }
  }

  /* 2. Fallback: Front-Matter-Schedule aus der .md laden */
  if (blob === null) {
    try {
      const entry = await getEntry('events', eventId);
      const items = entry?.data.schedule ?? [];
      const normalized = { items };
      if (store) {
        try {
          await store.set(key, JSON.stringify(normalized), { metadata: { eventId } });
        } catch (err) {
          console.error('blob set failed', err);
        }
      }
      return new Response(JSON.stringify(normalized), {
        status: 201,
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (err) {
      console.error('seed failed', err);
      return new Response(JSON.stringify({ items: [] }), {
        status: 201,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }
  return new Response(JSON.stringify(blob), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  });
}

export const prerender = false;
