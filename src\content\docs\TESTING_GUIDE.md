---
title: "Testing Guide"
description: "Hier findest du eine detaillierte Anleitung, um die USC Perchtoldsdorf-Website zu testen."
category: "Dev"
order: 14
---

# Testing Guide (Deutsch)
Diese Datei enthält eine deutsche Kurzfassung der wichtigsten Punkte.

---

This document provides instructions for setting up, running, and fixing tests for the USC Perchtoldsdorf website.

## Overview

The project uses Playwright for end-to-end testing. Tests are located in the `tests` directory and cover various aspects of the website functionality.

## Current Testing Status

There are currently issues with the Playwright testing setup related to module imports. The error occurs because of a mismatch between ES module syntax in test files and CommonJS syntax in the configuration.

## Fixing the Testing Setup

To resolve the current testing issues:

1. **Reinstall Playwright dependencies**:
   ```bash
   npm install @playwright/test --save-dev
   ```

2. **Install browser binaries**:
   ```bash
   npx playwright install
   ```

3. **Update the Playwright configuration**:
   
   Edit `playwright.config.js` to use ES module syntax:
   
   ```javascript
   // @ts-check
   import { defineConfig, devices } from '@playwright/test';

   /**
    * @see https://playwright.dev/docs/test-configuration
    */
   export default defineConfig({
     testDir: './tests',
     /* Maximum time one test can run for. */
     timeout: 30 * 1000,
     expect: {
       /**
        * Maximum time expect() should wait for the condition to be met.
        * For example in `await expect(locator).toHaveText();`
        */
       timeout: 5000
     },
     /* Run tests in files in parallel */
     fullyParallel: true,
     /* Fail the build on CI if you accidentally left test.only in the source code. */
     forbidOnly: !!process.env.CI,
     /* Retry on CI only */
     retries: process.env.CI ? 2 : 0,
     /* Opt out of parallel tests on CI. */
     workers: process.env.CI ? 1 : undefined,
     /* Reporter to use. See https://playwright.dev/docs/test-reporters */
     reporter: 'html',
     /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
     use: {
       /* Maximum time each action such as `click()` can take. Defaults to 0 (no limit). */
       actionTimeout: 0,
       /* Base URL to use in actions like `await page.goto('/')`. */
       baseURL: 'http://localhost:4321',

       /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
       trace: 'on-first-retry',
     },

     /* Configure projects for major browsers */
     projects: [
       {
         name: 'chromium',
         use: { ...devices['Desktop Chrome'] },
       },

       {
         name: 'firefox',
         use: { ...devices['Desktop Firefox'] },
       },

       {
         name: 'webkit',
         use: { ...devices['Desktop Safari'] },
       },

       /* Test against mobile viewports. */
       {
         name: 'Mobile Chrome',
         use: { ...devices['Pixel 5'] },
       },
       {
         name: 'Mobile Safari',
         use: { ...devices['iPhone 12'] },
       },
     ],

     /* Run your local dev server before starting the tests */
     webServer: {
       command: 'npm run dev',
       port: 4321,
       reuseExistingServer: !process.env.CI,
     },
   });
   ```

4. **Run the tests**:
   ```bash
   npm run test
   ```

## Available Tests

The project includes the following test suites:

1. **Search Functionality Tests** (`search.test.js`)
   - Tests site-wide search
   - Tests search with no results

2. **Admin Interface Tests** (`admin-interface.test.js`)
   - Tests pagination and filtering in the admin interface
   - Tests order and product management

3. **Import Scripts Tests** (`import-scripts.test.js`)
   - Tests the import of products and orders from Jimdo

4. **Email Integration Tests** (`email-integration.test.js`)
   - Tests contact form validation and submission
   - Tests newsletter subscription

## Running Specific Tests

To run a specific test suite:

```bash
npm run test tests/search.test.js
```

To run tests with UI mode (for debugging):

```bash
npm run test:ui
```

## Writing New Tests

When writing new tests, follow these guidelines:

1. **File Organization**:
   - Place tests in the `tests` directory
   - Name test files descriptively (e.g., `snipcart-integration.test.js`)

2. **Test Structure**:
   - Group related tests using `test.describe()`
   - Use `test.beforeEach()` for common setup
   - Make tests independent of each other

3. **Best Practices**:
   - Use page objects for complex pages
   - Mock external services when appropriate
   - Add comments explaining complex test logic

Example test structure:

```javascript
// @ts-check
import { test, expect } from '@playwright/test';

test.describe('Feature Name', () => {
  test.beforeEach(async ({ page }) => {
    // Common setup
    await page.goto('/');
  });

  test('should do something specific', async ({ page }) => {
    // Test implementation
    await page.click('button');
    await expect(page.locator('.result')).toBeVisible();
  });

  test('should handle edge cases', async ({ page }) => {
    // Edge case testing
    await page.fill('input', 'invalid value');
    await page.click('button');
    await expect(page.locator('.error')).toBeVisible();
  });
});
```

## Pre-Launch Testing Checklist

Before launching the site, perform the following tests:

1. **Functional Testing**:
   - All links work correctly
   - Forms submit properly
   - Search functionality works
   - E-commerce features function correctly
   - User authentication works

2. **Cross-Browser Testing**:
   - Test in Chrome, Firefox, Safari, and Edge
   - Test on mobile devices (iOS and Android)

3. **Performance Testing**:
   - Check page load times
   - Verify image optimization
   - Test JavaScript performance

4. **Accessibility Testing**:
   - Test keyboard navigation
   - Check screen reader compatibility
   - Verify color contrast

5. **Content Testing**:
   - Verify all content is present and correctly formatted
   - Check for spelling and grammar errors
   - Ensure images display properly

## Comparison with Jimdo Site

Create a detailed comparison between the current Jimdo site and the new Netlify site:

1. **Feature Comparison**:
   - List all features on the Jimdo site
   - Verify each feature is implemented on the new site
   - Note any differences or improvements

2. **Content Comparison**:
   - Check all pages exist on both sites
   - Verify content matches or has been improved
   - Ensure all images and media are present

3. **Performance Comparison**:
   - Compare page load times
   - Compare mobile responsiveness
   - Compare user experience

## Troubleshooting Common Issues

1. **Tests Failing Due to Timing Issues**:
   - Increase timeouts for specific actions
   - Use `waitFor` functions instead of fixed delays

2. **Element Not Found Errors**:
   - Check if selectors have changed
   - Use more robust selectors (data attributes are preferred)
   - Ensure the element is in the viewport

3. **Authentication Issues in Tests**:
   - Mock authentication when possible
   - Create test-specific users
   - Handle authentication tokens properly

## Resources

- [Playwright Documentation](https://playwright.dev/docs/intro)
- [Playwright API Reference](https://playwright.dev/docs/api/class-playwright)
- [Testing Best Practices](https://playwright.dev/docs/best-practices)
