# USC Perchtoldsdorf Website Tests

This directory contains tests for the USC Perchtoldsdorf website using Playwright.

## Test Structure

- `search.test.js`: Tests for the search functionality
- `admin-interface.test.js`: Tests for the admin interface
- `import-scripts.test.js`: Tests for the import scripts
- `shop.test.js`: Tests for the shop page functionality
- `responsive-layout.test.js`: Tests for responsive layout elements

## Running Tests

To run all tests:

```bash
npm test
```

To run a specific test:

```bash
npm run test:search
npm run test:admin
npm run test:import
npm run test:shop
```

To run tests with the Playwright UI:

```bash
npm run test:ui
```

## Test Requirements

- Node.js 18 or higher
- Playwright installed (`npm install -D @playwright/test`)
- A local development server running (`npm run dev`)

## Test Configuration

The tests are configured in `playwright.config.js` in the root directory. The configuration includes:

- Browser configurations (Chromium, Firefox, WebKit)
- Mobile device configurations
- Test timeouts
- Base URL for tests
- Web server configuration

## Writing New Tests

When writing new tests, follow these guidelines:

1. Create a new test file in the `tests` directory
2. Use descriptive test names
3. Group related tests using `test.describe()`
4. Use `test.beforeEach()` for setup code
5. Use `test.afterEach()` for cleanup code
6. Use `expect()` for assertions
7. Add the test to the `package.json` scripts

Example:

```javascript
import { test, expect } from '@playwright/test';

test.describe('Feature Name', () => {
  test('should do something', async ({ page }) => {
    await page.goto('/');
    await page.click('button');
    expect(await page.isVisible('text=Success')).toBeTruthy();
  });
});
```

## Continuous Integration

The tests are configured to run in CI environments with the following settings:

- Retries: 2 (only in CI)
- Workers: 1 (only in CI)
- Forbid `test.only`: true (only in CI)

## Troubleshooting

If tests are failing, check the following:

1. Make sure the development server is running
2. Check that the test data is available
3. Look for timing issues (increase timeouts if necessary)
4. Check for selector changes in the UI
5. Run tests with the UI to see what's happening
