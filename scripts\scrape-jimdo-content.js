/**
 * Scrape Content from Jimdo Site
 *
 * This script scrapes content from the old Jimdo site and generates content files
 * for each collection (news, teams, sponsors, events).
 */

const fs = require('fs');
const path = require('path');
const { chromium } = require('playwright');
const { parse } = require('csv-parse/sync');
const slugify = require('slugify');

// Configuration
const OLD_SITE_URL = 'https://www.usc-perchtoldsdorf.at';
const OUTPUT_DIR = path.join(__dirname, '../src/content');
const UPLOADS_DIR = path.join(__dirname, '../public/uploads');
const LOG_FILE = path.join(__dirname, '../logs/scrape-jimdo-content.log');

// Ensure output directories exist
if (!fs.existsSync(path.dirname(LOG_FILE))) {
  fs.mkdirSync(path.dirname(LOG_FILE), { recursive: true });
}

// Logging function
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

  console.log(logMessage);

  // Append to log file
  fs.appendFileSync(LOG_FILE, logMessage + '\n');
}

// Helper function to create a slug from a title
function createSlug(title) {
  return slugify(title, {
    lower: true,
    strict: true,
    locale: 'de'
  });
}

// Helper function to download an image
async function downloadImage(url, category, filename) {
  try {
    // Create directory if it doesn't exist
    const dir = path.join(UPLOADS_DIR, category);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Download image
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    const filePath = path.join(dir, filename);
    fs.writeFileSync(filePath, Buffer.from(buffer));

    return `/uploads/${category}/${filename}`;
  } catch (error) {
    log(`Error downloading image ${url}: ${error.message}`, 'error');
    return null;
  }
}

// Function to scrape news from the Jimdo site
async function scrapeNews(page) {
  log('Scraping news...');

  const news = [];

  try {
    // Navigate to the news page
    await page.goto(`${OLD_SITE_URL}/news/`);

    // Get news articles
    const newsArticles = await page.$$('article, .j-blog-post');

    for (const article of newsArticles) {
      try {
        const title = await article.$eval('h1, h2, h3, .j-blog-headline', el => el.textContent.trim());
        const date = await article.$eval('time, .datetime', el => el.textContent.trim());
        const excerpt = await article.$eval('p, .j-blog-content', el => el.textContent.trim().substring(0, 150) + '...');

        // Get image if available
        let imageUrl = null;
        try {
          imageUrl = await article.$eval('img', img => img.src);
        } catch (error) {
          // No image found
        }

        // Download image if available
        let localImagePath = null;
        if (imageUrl) {
          const filename = `news_${Math.random().toString(36).substring(2, 10)}.${imageUrl.split('.').pop()}`;
          localImagePath = await downloadImage(imageUrl, 'news', filename);
        }

        // Parse date
        let parsedDate;
        try {
          // Try to parse German date format (DD.MM.YYYY)
          const dateParts = date.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})/);
          if (dateParts) {
            parsedDate = new Date(dateParts[3], dateParts[2] - 1, dateParts[1]);
          } else {
            // Try to parse ISO date
            parsedDate = new Date(date);
          }

          // If date is invalid, use current date
          if (isNaN(parsedDate.getTime())) {
            parsedDate = new Date();
          }
        } catch (error) {
          parsedDate = new Date();
        }

        news.push({
          title,
          date: parsedDate.toISOString(),
          image: localImagePath,
          excerpt,
          content: excerpt,
          slug: createSlug(title)
        });
      } catch (error) {
        log(`Error scraping news article: ${error.message}`, 'error');
      }
    }

    log(`Scraped ${news.length} news articles`);
  } catch (error) {
    log(`Error scraping news: ${error.message}`, 'error');
  }

  return news;
}

// Function to scrape teams from the Jimdo site
async function scrapeTeams(page) {
  log('Scraping teams...');

  const teams = [];

  try {
    // Navigate to the teams page
    await page.goto(`${OLD_SITE_URL}/mannschaften/`);

    // Get team links
    const teamLinks = await page.$$eval('a[href*="/mannschaften/"]', links => {
      return links.map(link => ({
        title: link.textContent.trim(),
        url: link.href
      }));
    });

    // Filter out the main teams page and duplicates
    const uniqueTeamLinks = teamLinks.filter(team =>
      team.url !== `${OLD_SITE_URL}/mannschaften/` &&
      !teams.some(t => t.url === team.url)
    );

    for (const teamLink of uniqueTeamLinks) {
      try {
        // Navigate to the team page
        await page.goto(teamLink.url);

        // Get team details
        const title = teamLink.title;

        // Get team image if available
        let imageUrl = null;
        try {
          imageUrl = await page.$eval('.cc-m-image-align-1 img, .j-imageSubtitle img', img => img.src);
        } catch (error) {
          // No image found
        }

        // Download image if available
        let localImagePath = null;
        if (imageUrl) {
          const filename = `team_${Math.random().toString(36).substring(2, 10)}.${imageUrl.split('.').pop()}`;
          localImagePath = await downloadImage(imageUrl, 'teams', filename);
        }

        // Get team content
        let content = '';
        try {
          content = await page.$eval('.cc-m-text, .j-text', el => el.textContent.trim());
        } catch (error) {
          // No content found
        }

        // Determine team category
        let category = 'Nachwuchs';
        if (title.toLowerCase().includes('kampfmannschaft')) {
          category = 'Kampfmannschaft';
        } else if (title.toLowerCase().includes('u23')) {
          category = 'U23';
        } else if (title.toLowerCase().includes('special')) {
          category = 'Special Kickers';
        }

        teams.push({
          title,
          category,
          image: localImagePath,
          coach: 'Trainer Name', // Default value
          trainingTimes: 'Trainingszeiten werden noch bekannt gegeben', // Default value
          content,
          slug: createSlug(title)
        });
      } catch (error) {
        log(`Error scraping team ${teamLink.title}: ${error.message}`, 'error');
      }
    }

    log(`Scraped ${teams.length} teams`);
  } catch (error) {
    log(`Error scraping teams: ${error.message}`, 'error');
  }

  return teams;
}

// Function to scrape sponsors from the Jimdo site
async function scrapeSponsors(page) {
  log('Scraping sponsors...');

  const sponsors = [];

  try {
    // Navigate to the sponsors page
    await page.goto(`${OLD_SITE_URL}/sponsoren/`);

    // Get sponsor images
    const sponsorImages = await page.$$('img[src*="sponsor"], .sponsor img');

    for (const sponsorImage of sponsorImages) {
      try {
        const alt = await sponsorImage.getAttribute('alt') || 'Sponsor';
        const src = await sponsorImage.getAttribute('src');

        // Download image
        const filename = `sponsor_${Math.random().toString(36).substring(2, 10)}.${src.split('.').pop()}`;
        const localImagePath = await downloadImage(src, 'sponsors', filename);

        // Determine sponsor category
        let category = 'Partner';

        // Get parent element dimensions to determine sponsor category
        const parentBoundingBox = await sponsorImage.evaluate(img => {
          const parent = img.parentElement;
          return {
            width: parent.offsetWidth,
            height: parent.offsetHeight
          };
        });

        if (parentBoundingBox.width > 500) {
          category = 'Hauptsponsor';
        } else if (parentBoundingBox.width > 300) {
          category = 'Premium Partner';
        } else {
          category = 'Unterstützer';
        }

        sponsors.push({
          title: alt,
          logo: localImagePath,
          website: '',
          category,
          content: `# ${alt}\n\nWir danken ${alt} für die Unterstützung des USC Perchtoldsdorf.`,
          slug: createSlug(alt)
        });
      } catch (error) {
        log(`Error scraping sponsor image: ${error.message}`, 'error');
      }
    }

    log(`Scraped ${sponsors.length} sponsors`);
  } catch (error) {
    log(`Error scraping sponsors: ${error.message}`, 'error');
  }

  return sponsors;
}

// Function to scrape events from the Jimdo site
async function scrapeEvents(page) {
  log('Scraping events...');

  const events = [];

  try {
    // Navigate to the events page (if it exists)
    try {
      await page.goto(`${OLD_SITE_URL}/veranstaltungen/`);
    } catch (error) {
      // Events page might not exist, try another URL
      try {
        await page.goto(`${OLD_SITE_URL}/termine/`);
      } catch (error) {
        // No events page found, create sample events
        log('No events page found, creating sample events');

        // Create sample events for the next 3 months
        const today = new Date();

        for (let i = 0; i < 5; i++) {
          const eventDate = new Date(today);
          eventDate.setDate(today.getDate() + (i * 14) + 7); // Every two weeks

          const title = `Heimspiel gegen SV Musterstadt ${i + 1}`;
          const location = 'Sportplatz Perchtoldsdorf';
          const excerpt = `Heimspiel der Kampfmannschaft gegen SV Musterstadt ${i + 1}`;

          events.push({
            title,
            date: eventDate.toISOString(),
            endDate: new Date(eventDate.getTime() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours later
            location,
            image: null,
            excerpt,
            content: `# ${title}\n\n${excerpt}\n\n## Details\n\n- **Datum:** ${eventDate.toLocaleDateString('de-AT')}\n- **Uhrzeit:** ${eventDate.toLocaleTimeString('de-AT', { hour: '2-digit', minute: '2-digit' })}\n- **Ort:** ${location}`,
            featured: i === 0, // First event is featured
            category: 'Spiel',
            slug: createSlug(title)
          });
        }

        return events;
      }
    }

    // Get event elements
    const eventElements = await page.$$('.j-blog-post, article, .event-item');

    for (const eventElement of eventElements) {
      try {
        const title = await eventElement.$eval('h1, h2, h3, .j-blog-headline', el => el.textContent.trim());

        // Try to find date
        let dateText = '';
        try {
          dateText = await eventElement.$eval('time, .datetime, .date', el => el.textContent.trim());
        } catch (error) {
          // No date found, use current date + random days
          const randomDays = Math.floor(Math.random() * 30) + 1;
          const eventDate = new Date();
          eventDate.setDate(eventDate.getDate() + randomDays);
          dateText = eventDate.toLocaleDateString('de-AT');
        }

        // Parse date
        let parsedDate;
        try {
          // Try to parse German date format (DD.MM.YYYY)
          const dateParts = dateText.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})/);
          if (dateParts) {
            parsedDate = new Date(dateParts[3], dateParts[2] - 1, dateParts[1]);
          } else {
            // Try to parse ISO date
            parsedDate = new Date(dateText);
          }

          // If date is invalid, use current date + random days
          if (isNaN(parsedDate.getTime())) {
            const randomDays = Math.floor(Math.random() * 30) + 1;
            parsedDate = new Date();
            parsedDate.setDate(parsedDate.getDate() + randomDays);
          }
        } catch (error) {
          const randomDays = Math.floor(Math.random() * 30) + 1;
          parsedDate = new Date();
          parsedDate.setDate(parsedDate.getDate() + randomDays);
        }

        // Get location
        let location = 'Sportplatz Perchtoldsdorf';
        try {
          location = await eventElement.$eval('.location, .place', el => el.textContent.trim());
        } catch (error) {
          // Use default location
        }

        // Get excerpt
        let excerpt = '';
        try {
          excerpt = await eventElement.$eval('p, .j-blog-content', el => el.textContent.trim().substring(0, 150) + '...');
        } catch (error) {
          excerpt = `Veranstaltung am ${parsedDate.toLocaleDateString('de-AT')} in ${location}`;
        }

        // Get image if available
        let imageUrl = null;
        try {
          imageUrl = await eventElement.$eval('img', img => img.src);
        } catch (error) {
          // No image found
        }

        // Download image if available
        let localImagePath = null;
        if (imageUrl) {
          const filename = `event_${Math.random().toString(36).substring(2, 10)}.${imageUrl.split('.').pop()}`;
          localImagePath = await downloadImage(imageUrl, 'events', filename);
        }

        // Determine event category
        let category = 'Sonstiges';
        if (title.toLowerCase().includes('spiel') || title.toLowerCase().includes('match')) {
          category = 'Spiel';
        } else if (title.toLowerCase().includes('training')) {
          category = 'Training';
        } else if (title.toLowerCase().includes('feier') || title.toLowerCase().includes('fest')) {
          category = 'Feier';
        }

        events.push({
          title,
          date: parsedDate.toISOString(),
          endDate: new Date(parsedDate.getTime() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours later
          location,
          image: localImagePath,
          excerpt,
          content: `# ${title}\n\n${excerpt}\n\n## Details\n\n- **Datum:** ${parsedDate.toLocaleDateString('de-AT')}\n- **Uhrzeit:** ${parsedDate.toLocaleTimeString('de-AT', { hour: '2-digit', minute: '2-digit' })}\n- **Ort:** ${location}`,
          featured: false,
          category,
          slug: createSlug(title)
        });
      } catch (error) {
        log(`Error scraping event: ${error.message}`, 'error');
      }
    }

    log(`Scraped ${events.length} events`);
  } catch (error) {
    log(`Error scraping events: ${error.message}`, 'error');
  }

  return events;
}

// Function to generate content files
function generateContentFiles(collection, items) {
  log(`Generating ${collection} content files...`);

  const collectionDir = path.join(OUTPUT_DIR, collection);

  // Ensure directory exists
  if (!fs.existsSync(collectionDir)) {
    fs.mkdirSync(collectionDir, { recursive: true });
  }

  for (const item of items) {
    try {
      // Generate filename
      let filename;
      if (collection === 'news' || collection === 'events') {
        // Use date in filename for news and events
        const date = new Date(item.date);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        filename = `${year}-${month}-${day}-${item.slug}.md`;
      } else {
        // Use slug for other collections
        filename = `${item.slug}.md`;
      }

      // Generate frontmatter
      let frontmatter = '---\n';

      // Add frontmatter fields based on collection
      if (collection === 'news') {
        frontmatter += `title: ${JSON.stringify(item.title)}\n`;
        frontmatter += `date: ${item.date}\n`;
        if (item.image) frontmatter += `image: ${item.image}\n`;
        frontmatter += `excerpt: ${JSON.stringify(item.excerpt)}\n`;
        frontmatter += `tags: ["USC Perchtoldsdorf", "News"]\n`;
        frontmatter += `author: "USC Perchtoldsdorf"\n`;
      } else if (collection === 'teams') {
        frontmatter += `title: ${JSON.stringify(item.title)}\n`;
        frontmatter += `category: ${item.category}\n`;
        if (item.image) frontmatter += `image: ${item.image}\n`;
        frontmatter += `coach: ${JSON.stringify(item.coach)}\n`;
        frontmatter += `trainingTimes: ${JSON.stringify(item.trainingTimes)}\n`;
      } else if (collection === 'sponsors') {
        frontmatter += `title: ${JSON.stringify(item.title)}\n`;
        if (item.logo) frontmatter += `logo: ${item.logo}\n`;
        if (item.website) frontmatter += `website: ${JSON.stringify(item.website)}\n`;
        frontmatter += `category: ${item.category}\n`;
      } else if (collection === 'events') {
        frontmatter += `title: ${JSON.stringify(item.title)}\n`;
        frontmatter += `date: ${item.date}\n`;
        frontmatter += `endDate: ${item.endDate}\n`;
        frontmatter += `location: ${JSON.stringify(item.location)}\n`;
        if (item.image) frontmatter += `image: ${item.image}\n`;
        frontmatter += `excerpt: ${JSON.stringify(item.excerpt)}\n`;
        frontmatter += `featured: ${item.featured}\n`;
        frontmatter += `category: ${item.category}\n`;
      } else if (collection === 'pages') {
        frontmatter += `title: ${JSON.stringify(item.title)}\n`;
        if (item.heroTitle) frontmatter += `heroTitle: ${JSON.stringify(item.heroTitle)}\n`;
        if (item.heroSubtitle) frontmatter += `heroSubtitle: ${JSON.stringify(item.heroSubtitle)}\n`;
        if (item.heroImage) frontmatter += `heroImage: ${item.heroImage}\n`;

        // For home page, add additional fields
        if (item.slug === 'home') {
          frontmatter += `aboutText: "USC Perchtoldsdorf ist ein Fußballverein mit langer Tradition. Seit 1921 bieten wir Fußball für alle Altersgruppen an."\n`;
          frontmatter += `ctaText: "Werde Teil unseres Vereins!"\n`;
          frontmatter += `ctaSubtext: "Melde dich an und sei dabei!"\n`;
        }
      }

      frontmatter += '---\n\n';

      // Add content
      const content = item.content || '';

      // Write file
      const filePath = path.join(collectionDir, filename);
      fs.writeFileSync(filePath, frontmatter + content);

      log(`Generated ${collection} file: ${filename}`);
    } catch (error) {
      log(`Error generating ${collection} file for ${item.title}: ${error.message}`, 'error');
    }
  }

  log(`Generated ${items.length} ${collection} files`);
}

// Function to scrape static pages from the Jimdo site
async function scrapeStaticPages(page) {
  log('Scraping static pages...');

  const staticPages = [];

  try {
    // Get main navigation links
    await page.goto(OLD_SITE_URL);

    // Get all navigation links
    const navLinks = await page.$$eval('nav a, .navigation a, .menu a, header a', links => {
      return links.map(link => ({
        title: link.textContent.trim(),
        url: link.href
      }));
    });

    // Filter out external links and duplicates
    const uniquePageLinks = navLinks.filter(link =>
      link.url.startsWith(OLD_SITE_URL) &&
      !link.url.includes('/news/') &&
      !link.url.includes('/mannschaften/') &&
      !link.url.includes('/sponsoren/') &&
      !link.url.includes('/shop/') &&
      !link.url.includes('/veranstaltungen/') &&
      !link.url.includes('/termine/') &&
      !staticPages.some(p => p.url === link.url)
    );

    for (const pageLink of uniquePageLinks) {
      try {
        // Navigate to the page
        await page.goto(pageLink.url);

        // Get page title
        const title = await page.title();

        // Get page content
        let content = '';
        try {
          // Try to get main content
          content = await page.$eval('main, .content, .j-text, article', el => el.innerHTML);
        } catch (error) {
          // If main content not found, try to get body content
          try {
            content = await page.$eval('body', el => {
              // Remove header, footer, navigation, etc.
              const clone = el.cloneNode(true);
              const elementsToRemove = clone.querySelectorAll('header, footer, nav, .navigation, .menu, .sidebar, .j-footer');
              elementsToRemove.forEach(el => el.remove());
              return clone.innerHTML;
            });
          } catch (innerError) {
            log(`Error getting content for ${pageLink.url}: ${innerError.message}`, 'error');
          }
        }

        // Get hero image if available
        let heroImage = null;
        try {
          heroImage = await page.$eval('header img, .hero img, .banner img, .j-header img', img => img.src);
        } catch (error) {
          // No hero image found
        }

        // Download hero image if available
        let localHeroImagePath = null;
        if (heroImage) {
          const filename = `page_hero_${Math.random().toString(36).substring(2, 10)}.${heroImage.split('.').pop()}`;
          localHeroImagePath = await downloadImage(heroImage, 'pages', filename);
        }

        // Extract page slug from URL
        const urlPath = new URL(pageLink.url).pathname;
        const slug = urlPath === '/' ? 'home' : urlPath.split('/').filter(Boolean).pop() || 'page';

        // Create page object
        staticPages.push({
          title: title || pageLink.title,
          slug,
          url: pageLink.url,
          heroImage: localHeroImagePath,
          heroTitle: title || pageLink.title,
          heroSubtitle: '',
          content: content || '',
        });

        log(`Scraped static page: ${pageLink.title} (${pageLink.url})`);
      } catch (error) {
        log(`Error scraping static page ${pageLink.url}: ${error.message}`, 'error');
      }
    }

    log(`Scraped ${staticPages.length} static pages`);
  } catch (error) {
    log(`Error scraping static pages: ${error.message}`, 'error');
  }

  return staticPages;
}

// Main function
async function main() {
  log('Starting Jimdo content scraping...');

  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Scrape static pages
    const staticPages = await scrapeStaticPages(page);
    generateContentFiles('pages', staticPages);

    // Scrape news
    const news = await scrapeNews(page);
    generateContentFiles('news', news);

    // Scrape teams
    const teams = await scrapeTeams(page);
    generateContentFiles('teams', teams);

    // Scrape sponsors
    const sponsors = await scrapeSponsors(page);
    generateContentFiles('sponsors', sponsors);

    // Scrape events
    const events = await scrapeEvents(page);
    generateContentFiles('events', events);

    log('Content scraping completed successfully');
  } catch (error) {
    log(`Error in main process: ${error.message}`, 'error');
  } finally {
    await browser.close();
  }
}

// Run the main function
main().catch(error => {
  log(`Unhandled error: ${error.message}`, 'error');
  process.exit(1);
});
