---
title: "Decap CMS Implementation Documentation"
description: "Hier findest du eine detaillierte Anleitung, um Netilfy CMS (ehemalig Decap CMS) auf der USC Perchtoldsdorf-Website zu implementieren."
category: "Dev"
order: 6
---

# Decap CMS Implementation Documentation (Deutsch)
Diese Datei enthält eine deutsche Kurzfassung der wichtigsten Punkte.

---

This document outlines the implementation details of the Decap CMS integration for the USC Perchtoldsdorf website.

## Overview

The website uses Decap CMS (formerly Netlify CMS) for content management, integrated with Netlify Identity for authentication. The CMS is configured to manage various content types through Astro's content collections.

## Authentication Flow

### Netlify Identity Integration

The authentication system uses Netlify Identity with the following components:

1. **Netlify Identity Widget**: Loaded on all pages via `src/components/NetlifyIdentityHelper.astro`
2. **Authentication Handler**: Client-side script in `public/js/identity-redirect.js` that handles authentication tokens
3. **Auth Page**: Dedicated page at `src/pages/auth.astro` for processing authentication tokens
4. **Debug Page**: Utility page at `public/debug-identity.html` for troubleshooting authentication issues

### Authentication Process

1. User receives an invitation email from Netlify
2. User clicks the invitation link, which contains an authentication token
3. The token is processed by the identity-redirect.js script
4. The user is prompted to set a password
5. After successful authentication, the user is redirected to the admin dashboard

## Content Structure

### Content Collections

The following content collections are defined in `src/content/config.ts`:

1. **News**: Blog posts and news articles
2. **Teams**: Team information and player rosters
3. **Sponsors**: Sponsor information and logos
4. **Products**: Shop products
5. **Pages**: Static page content
6. **Events**: Calendar events, matches, and activities
7. **Settings**: Global site settings like navigation and footer

### Directory Structure

```
src/
├── content/
│   ├── news/
│   ├── teams/
│   ├── sponsors/
│   ├── products/
│   ├── pages/
│   ├── events/
│   ├── settings/
│   └── config.ts
```

## CMS Configuration

The CMS is configured in `public/admin/config.yml` with the following key settings:

```yaml
backend:
  name: git-gateway
  branch: main

media_folder: "public/uploads"
public_folder: "/uploads"

collections:
  - name: "news"
    # ...
  - name: "teams"
    # ...
  - name: "sponsors"
    # ...
  - name: "products"
    # ...
  - name: "pages"
    # ...
  - name: "events"
    # ...
  - name: "settings"
    # ...
```

## Admin Routes

The admin section has the following route structure:

1. **Admin Landing Page**: `src/pages/admin.html` - Entry point that checks authentication and provides navigation options
2. **Admin Dashboard**: `src/pages/admin/dashboard.astro` - Main dashboard with statistics and quick links
3. **CMS Interface**: `/admin/cms` - Dedicated route for Decap CMS that doesn't redirect to dashboard

### Admin Dashboard

A custom admin dashboard is implemented at `src/pages/admin/dashboard.astro` with the following features:

1. **Statistics Cards**: Display counts of content items by type
2. **Recent Orders**: Show recent shop orders
3. **Quick Links**: Provide easy access to different content sections
4. **Action Buttons**: Common administrative actions including a link to the CMS

## Implementation Timeline

### Phase 1: Authentication Setup (Completed)
- Implemented Netlify Identity integration
- Created authentication handling scripts
- Fixed CORS and redirect issues

### Phase 2: Content Structure Enhancement (Completed)
- Added Events collection
- Added Settings collection for navigation and footer
- Added Contact page management
- Created sample content

### Phase 3: Admin Dashboard Enhancement (Completed)
- Updated dashboard UI to show all content types
- Added quick links to new content sections
- Improved error handling and TypeScript types

### Phase 4: Admin Route Optimization (Completed)
- Created dedicated /admin/cms route for Decap CMS
- Improved admin landing page with better navigation
- Fixed route collision issues
- Updated dashboard links to point to the new CMS route

### Phase 5: Documentation (Completed)
- Created content editor guide
- Created technical implementation documentation

## Technical Considerations

### Error Handling

The implementation includes robust error handling:

- Try/catch blocks for collection loading
- Fallbacks for missing collections
- TypeScript type annotations for better code quality

### Performance Optimization

- Lazy loading of the Netlify Identity widget
- Efficient content collection queries
- Optimized dashboard rendering

## Future Enhancements

1. **Custom Preview Templates**: Add custom preview templates for better content editing experience
2. **Media Library Enhancements**: Improve media organization and processing
3. **Validation Rules**: Add more validation rules to content schemas
4. **Admin Pages for New Content Types**: Create dedicated admin pages for events, teams, etc.
5. **Import/Export Tools**: Enhance data import/export capabilities

## Troubleshooting

### Common Issues

1. **Authentication Failures**:
   - Check CORS settings in netlify.toml
   - Verify Netlify Identity is properly configured
   - Check browser console for errors

2. **Content Not Saving**:
   - Verify Git Gateway is enabled in Netlify
   - Check user permissions
   - Look for validation errors in the CMS

3. **Local Development Issues**:
   - Set netlifySiteURL in localStorage
   - Use the debug-identity.html page for troubleshooting
   - Check for CORS errors in the console

## References

- [Decap CMS Documentation](https://decapcms.org/docs/intro/)
- [Netlify Identity Documentation](https://docs.netlify.com/visitor-access/identity/)
- [Astro Content Collections](https://docs.astro.build/en/guides/content-collections/)
