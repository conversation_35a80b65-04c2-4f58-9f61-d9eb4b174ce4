/**
 * Tests for the import scripts
 * 
 * This test verifies that the import scripts work correctly, including:
 * - Importing products from CSV
 * - Importing orders from CSV
 * - Handling large datasets
 */

import { test, expect } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

test.describe('Import Scripts', () => {
  test('import-products.js should create product markdown files', async () => {
    // Run the import-products.js script
    const { stdout, stderr } = await execAsync('node scripts/import-products.js');
    
    // Check that the script ran without errors
    expect(stderr).toBe('');
    
    // Check that the script output indicates success
    expect(stdout).toContain('Import completed');
    
    // Check that product files were created
    const productsDir = path.join(process.cwd(), 'src/content/products');
    const productFiles = fs.readdirSync(productsDir).filter(file => file.endsWith('.md'));
    
    // Expect at least some product files to be created
    expect(productFiles.length).toBeGreaterThan(0);
    
    // Check the content of a product file
    const productFile = fs.readFileSync(path.join(productsDir, productFiles[0]), 'utf8');
    
    // Expect the file to have frontmatter
    expect(productFile).toContain('---');
    
    // Expect the file to have required fields
    expect(productFile).toContain('title:');
    expect(productFile).toContain('price:');
    expect(productFile).toContain('category:');
    expect(productFile).toContain('available:');
  });
  
  test('import-orders.js should create orders.json file', async () => {
    // Run the import-orders.js script
    const { stdout, stderr } = await execAsync('node scripts/import-orders.js');
    
    // Check that the script ran without errors
    expect(stderr).toBe('');
    
    // Check that the script output indicates success
    expect(stdout).toContain('Successfully imported');
    
    // Check that orders.json was created
    const ordersFile = path.join(process.cwd(), 'src/data/orders.json');
    expect(fs.existsSync(ordersFile)).toBe(true);
    
    // Check the content of the orders.json file
    const orders = JSON.parse(fs.readFileSync(ordersFile, 'utf8'));
    
    // Expect orders to be an array
    expect(Array.isArray(orders)).toBe(true);
    
    // Expect at least some orders
    expect(orders.length).toBeGreaterThan(0);
    
    // Check the structure of an order
    const order = orders[0];
    expect(order).toHaveProperty('orderNumber');
    expect(order).toHaveProperty('orderDate');
    expect(order).toHaveProperty('customer');
    expect(order).toHaveProperty('items');
    expect(order).toHaveProperty('totals');
    
    // Check that items is an array
    expect(Array.isArray(order.items)).toBe(true);
  });
  
  test('import scripts should handle large datasets', async () => {
    // This test assumes that the CSV files contain a large number of records
    
    // Run the import-products.js script
    const productsResult = await execAsync('node scripts/import-products.js');
    
    // Check that the script ran without errors
    expect(productsResult.stderr).toBe('');
    
    // Run the import-orders.js script
    const ordersResult = await execAsync('node scripts/import-orders.js');
    
    // Check that the script ran without errors
    expect(ordersResult.stderr).toBe('');
    
    // Check the number of product files
    const productsDir = path.join(process.cwd(), 'src/content/products');
    const productFiles = fs.readdirSync(productsDir).filter(file => file.endsWith('.md'));
    
    // Check the number of orders
    const ordersFile = path.join(process.cwd(), 'src/data/orders.json');
    const orders = JSON.parse(fs.readFileSync(ordersFile, 'utf8'));
    
    // Log the counts for information
    console.log(`Number of product files: ${productFiles.length}`);
    console.log(`Number of orders: ${orders.length}`);
    
    // The test passes if we get here without errors
  });
});
