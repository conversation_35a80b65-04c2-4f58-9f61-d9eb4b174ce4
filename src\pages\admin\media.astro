---
import AdminDashboardLayout from '../../layouts/AdminDashboardLayout.astro';
---

<AdminDashboardLayout title="Medienbibliothek - USC Perchtoldsdorf Admin">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">Medienbibliothek</h1>

    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
      <div class="p-6 border-b">
        <h2 class="text-xl font-semibold">Medien verwalten</h2>
      </div>
      <div class="p-6">
        <p class="text-gray-600 mb-6">
          Die Medienbibliothek wird über das CMS verwaltet. Klicken Sie auf den Button unten, um zum
          CMS zu gelangen und Medien hochzuladen, zu bearbeiten oder zu löschen.
        </p>

        <a
          href="/admin/cms/#/media"
          class="inline-flex items-center px-4 py-2 bg-usc-primary text-white rounded-md hover:bg-usc-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-usc-primary"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            ></path>
          </svg>
          Zum CMS Medienbereich
        </a>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Media Usage Guide -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 border-b">
          <h2 class="text-xl font-semibold">Medien verwenden</h2>
        </div>
        <div class="p-6">
          <p class="text-gray-600 mb-4">So verwenden Sie Medien in Ihren Inhalten:</p>

          <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">In Inhalten</h3>
            <ol class="list-decimal pl-5 space-y-2 text-gray-600">
              <li>Navigieren Sie zum CMS und bearbeiten Sie einen Inhalt.</li>
              <li>Klicken Sie auf das Bild-Symbol in der Symbolleiste.</li>
              <li>Wählen Sie ein Bild aus der Medienbibliothek oder laden Sie ein neues hoch.</li>
              <li>Das Bild wird in den Inhalt eingefügt.</li>
            </ol>
          </div>

          <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">In Produkten</h3>
            <ol class="list-decimal pl-5 space-y-2 text-gray-600">
              <li>Bearbeiten Sie ein Produkt im CMS.</li>
              <li>Klicken Sie auf das Bild-Feld.</li>
              <li>Wählen Sie ein Bild aus der Medienbibliothek oder laden Sie ein neues hoch.</li>
              <li>Speichern Sie das Produkt.</li>
            </ol>
          </div>

          <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg
                  class="h-5 w-5 text-blue-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-blue-700">
                  Bilder werden automatisch optimiert und in verschiedenen Größen gespeichert, um
                  die Ladezeit der Website zu verbessern.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Media Best Practices -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 border-b">
          <h2 class="text-xl font-semibold">Best Practices</h2>
        </div>
        <div class="p-6">
          <p class="text-gray-600 mb-4">
            Befolgen Sie diese Best Practices für die Verwendung von Medien auf der Website:
          </p>

          <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">Bildformate</h3>
            <ul class="list-disc pl-5 space-y-1 text-gray-600">
              <li>Verwenden Sie <strong>JPG</strong> für Fotos und komplexe Bilder.</li>
              <li>
                Verwenden Sie <strong>PNG</strong> für Bilder mit Transparenz oder einfachen Grafiken.
              </li>
              <li>Verwenden Sie <strong>SVG</strong> für Logos und Icons.</li>
              <li>
                Verwenden Sie <strong>WebP</strong> für beste Kompression (wird automatisch generiert).
              </li>
            </ul>
          </div>

          <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">Bildgrößen</h3>
            <ul class="list-disc pl-5 space-y-1 text-gray-600">
              <li>Produktbilder: 800x800 Pixel (quadratisch)</li>
              <li>Headerbilder: 1920x600 Pixel</li>
              <li>Teamfotos: 600x800 Pixel</li>
              <li>News-Bilder: 1200x800 Pixel</li>
              <li>Sponsorenlogos: 400x200 Pixel</li>
            </ul>
          </div>

          <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">Dateigrößen</h3>
            <ul class="list-disc pl-5 space-y-1 text-gray-600">
              <li>Halten Sie Bilder unter 500 KB für beste Performance.</li>
              <li>
                Komprimieren Sie Bilder vor dem Hochladen mit Tools wie <a
                  href="https://tinypng.com"
                  target="_blank"
                  class="text-blue-600 hover:text-blue-800">TinyPNG</a
                >.
              </li>
              <li>
                Vermeiden Sie das Hochladen von Bildern direkt von der Kamera ohne Bearbeitung.
              </li>
            </ul>
          </div>

          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg
                  class="h-5 w-5 text-yellow-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  Stellen Sie sicher, dass Sie die Rechte an allen hochgeladenen Bildern besitzen
                  oder die Erlaubnis zur Verwendung haben.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Media Storage Information -->
    <div class="mt-6 bg-white rounded-lg shadow-md overflow-hidden">
      <div class="p-6 border-b">
        <h2 class="text-xl font-semibold">Medienspeicher</h2>
      </div>
      <div class="p-6">
        <p class="text-gray-600 mb-4">
          Alle Medien werden in Netlify gespeichert und über ein CDN (Content Delivery Network)
          ausgeliefert, um schnelle Ladezeiten weltweit zu gewährleisten.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium mb-2">Speicherort</h3>
            <p class="text-gray-600">
              Alle Medien werden im <code class="bg-gray-100 px-2 py-1 rounded"
                >/public/uploads/</code
              > Verzeichnis gespeichert.
            </p>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium mb-2">Speicherlimit</h3>
            <p class="text-gray-600">
              Netlify bietet 100 GB Bandbreite und unbegrenzten Speicherplatz im kostenlosen Plan.
            </p>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium mb-2">Unterstützte Dateitypen</h3>
            <p class="text-gray-600">
              Bilder (JPG, PNG, GIF, WebP, SVG), Dokumente (PDF, DOC, DOCX), Videos (MP4, WebM),
              Audio (MP3, WAV).
            </p>
          </div>
        </div>

        <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="h-5 w-5 text-blue-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-blue-700">
                Für große Mediendateien wie Videos wird empfohlen, externe Dienste wie YouTube oder
                Vimeo zu verwenden und diese einzubetten, anstatt sie direkt hochzuladen.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</AdminDashboardLayout>
