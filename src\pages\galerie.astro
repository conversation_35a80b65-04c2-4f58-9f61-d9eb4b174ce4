---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import ImageCarousel from '../components/ImageCarousel.astro';
import LazyImage from '../components/LazyImage.astro';

import fs from 'fs';
import path from 'path';

/* ── <PERSON> ─────────────────────────────────────────────────────────── */
const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
const publicDir = path.join(process.cwd(), 'public');

/* ── Hilfs­funktionen ────────────────────────────────────────────────────── */
/** Rekursiv alle Bild-Pfad-Strings ab `/public` ermitteln */
function getImagePaths(dir: string): string[] {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  let images: string[] = [];

  for (const entry of entries) {
    const full = path.join(dir, entry.name);

    if (entry.isDirectory()) {
      images = images.concat(getImagePaths(full));
    } else if (/\.(jpe?g|png|gif|webp)$/i.test(entry.name)) {
      // führenden Slash für Astro-Assets beibehalten & Backslashes normalisieren
      const rel = '/' + path.relative(publicDir, full).replace(/\\/g, '/');
      images.push(rel);
    }
  }
  return images;
}

/* ── Daten vorbereiten ───────────────────────────────────────────────────── */
// Kategorien anhand der Unterordner in /public/uploads bestimmen
const categoryDirs = fs
  .readdirSync(uploadsDir, { withFileTypes: true })
  .filter((d) => d.isDirectory())
  .map((d) => d.name);

interface Category {
  name: string;
  images: string[];
}

function formatName(name: string): string {
  return name
    .replace(/[-_]/g, ' ')
    .replace(/\b\w/g, (l) => l.toUpperCase());
}

const categories: Category[] = categoryDirs
  .filter(name => name !== 'docs') // Exclude 'docs' category
  .map((name) => ({
    name,
    images: getImagePaths(path.join(uploadsDir, name)),
  }));

// Bilder im Wurzelordner (ohne Kategorie)
const rootImages = fs
  .readdirSync(uploadsDir)
  .filter((file) => /\.(jpe?g|png|gif|webp)$/i.test(file))
  .map((file) => '/uploads/' + file);

const allCategories: { name: string; title: string; images: string[] }[] = [
  ...(rootImages.length
    ? [{ name: 'misc', title: 'Sonstiges', images: rootImages }]
    : []),
  ...categories.map((c) => ({ name: c.name, title: formatName(c.name), images: c.images })),
];

const navCategories = allCategories.map(({ name, title }) => ({ name, title }));

const allImages: { category: string; src: string }[] = allCategories.flatMap((cat) =>
  cat.images.map((src) => ({ category: cat.name, src }))
);

type CarouselItem = { src: string; alt: string };
const carouselImages: CarouselItem[] = allImages
  .slice(0, 5)
  .map(({ src }): CarouselItem => ({ src, alt: 'Gallery image' }));
---

<Layout title="Galerie" description="Bildgalerie des USC Perchtoldsdorf">
  <section class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8 text-center">Galerie</h1>

    {carouselImages.length > 0 && (
      <div class="mb-12">
        <ImageCarousel images={carouselImages} height="h-64 md:h-96" />
      </div>
    )}

    <!-- Kategorie-Navigation -->
    <div class="flex flex-wrap justify-center gap-2 mb-8">
      <button
        data-category-button="all"
        class="category-btn bg-usc-primary text-white px-4 py-2 rounded text-sm"
      >
        Alle
      </button>
      {navCategories.map((c) => (
        <button
          data-category-button={c.name}
          class="category-btn bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded text-sm capitalize"
        >
          {c.title}
        </button>
      ))}
    </div>
    <div id="gallery">
      {allCategories.map((cat) => (
        <section class="mb-12" data-category-section={cat.name}>
          <h2 class="text-2xl font-semibold mb-4 capitalize">{cat.title}</h2>
          <div class="columns-2 md:columns-3 lg:columns-4 gap-4 [column-fill:_balance]">
            {cat.images.map((src, i) => (
              <LazyImage
                src={src}
                alt={`Galerie Bild ${i + 1}`}
                class="mb-4 rounded-lg shadow-md w-full"
              />
            ))}
          </div>
        </section>
      ))}
    </div>
  </section>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const buttons = document.querySelectorAll('.category-btn');
      const sections = document.querySelectorAll('[data-category-section]');

      buttons.forEach((btn) => {
        btn.addEventListener('click', () => {
          const selected = btn.getAttribute('data-category-button');

          buttons.forEach((b) => b.classList.remove('bg-usc-primary', 'text-white'));
          btn.classList.add('bg-usc-primary', 'text-white');

          sections.forEach((section) => {
            if (selected === 'all' || section.getAttribute('data-category-section') === selected) {
              section.classList.remove('hidden');
            } else {
              section.classList.add('hidden');
            }
          });
        });
      });
    });
  </script>
</Layout>
