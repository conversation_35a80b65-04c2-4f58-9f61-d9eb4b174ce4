---
title: "Deployment Guide"
description: "Hier findest du eine detaillierte Anleitung, um die USC Perchtoldsdorf-Website auf Netlify zu deployen."
category: "Dev"
order: 9
---

# Deployment Guide (Deutsch)
Diese Datei enthält eine deutsche Kurzfassung der wichtigsten Punkte.

---

This document provides instructions for deploying the USC Perchtoldsdorf website to Netlify.

## Prerequisites

Before deploying the website, you need:

1. A GitHub account
2. A Netlify account
3. A Google Analytics account (optional)

## Deployment Steps

### 1. Clone the Repository

```bash
git clone https://github.com/imKXNNY/USC-Perchtoldsdorf.git
cd USC-Perchtoldsdorf/astro-frontend
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Configure Environment Variables

Create a `.env` file in the root directory with the following variable:

```
PUBLIC_GA_MEASUREMENT_ID=your_ga_measurement_id
```

Replace `your_ga_measurement_id` with your Google Analytics measurement ID.

### 4. Build the Website

```bash
npm run build
```

### 5. Deploy to Netlify

#### Option 1: Deploy via Netlify CLI

1. Install the Netlify CLI:

```bash
npm install -g netlify-cli
```

2. Log in to Netlify:

```bash
netlify login
```

3. Initialize a new Netlify site:

```bash
netlify init
```

4. Follow the prompts to create a new site or link to an existing site.

5. Deploy the site:

```bash
netlify deploy --prod
```

#### Option 2: Deploy via Netlify UI

1. Log in to your Netlify account.
2. Click "New site from Git".
3. Select GitHub as the Git provider.
4. Authorize Netlify to access your GitHub account.
5. Select the USC-Perchtoldsdorf repository.
6. Configure the build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
7. Click "Deploy site".

### 6. Configure Environment Variables in Netlify

1. Go to your site's dashboard in Netlify.
2. Click "Site settings".
3. Click "Environment variables".
4. Add the following environment variable:
   - `PUBLIC_GA_MEASUREMENT_ID`: Your Google Analytics measurement ID

### 7. Configure Netlify Identity

1. Go to your site's dashboard in Netlify.
2. Click "Identity".
3. Click "Enable Identity".
4. Configure the registration preferences:
   - Open registration or invite only
   - External providers (optional)
   - Email templates (optional)
5. Configure the email templates:
   - Invitation template
   - Confirmation template
   - Recovery template
   - Email change template

### 8. Configure Netlify Forms

1. Go to your site's dashboard in Netlify.
2. Click "Forms".
3. Verify that the contact form is listed.
4. Configure form notifications (optional).

### 9. Configure Netlify Redirects

Create a `_redirects` file in the `public` directory with the following content:

```
/admin/* /admin/index.html 200
/*    /index.html   200
```

This ensures that the admin interface and client-side routing work correctly.

### 10. Configure Netlify Headers

Create a `_headers` file in the `public` directory with the following content:

```
/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
```

This adds security headers to all pages.

### 11. Configure Netlify CMS

1. Go to your site's dashboard in Netlify.
2. Click "Identity".
3. Click "Settings and usage".
4. Scroll down to "Git Gateway".
5. Click "Enable Git Gateway".

### 12. Test the Deployment

1. Visit your site's URL.
2. Test all functionality:
   - Navigation
   - Search
   - Shop
   - Contact form
   - Admin interface

### 13. Configure Custom Domain (Optional)

1. Go to your site's dashboard in Netlify.
2. Click "Domain settings".
3. Click "Add custom domain".
4. Enter your domain name.
5. Follow the instructions to configure your DNS settings.

## Continuous Deployment

Netlify automatically deploys your site when you push changes to the main branch of your GitHub repository. To deploy changes:

1. Make your changes locally.
2. Commit your changes:

```bash
git add .
git commit -m "Your commit message"
```

3. Push your changes:

```bash
git push origin main
```

4. Netlify will automatically build and deploy your site.

## Troubleshooting

### Build Failures

If your build fails, check the build logs in Netlify for error messages. Common issues include:

- Missing dependencies
- Syntax errors
- Environment variable issues

### Identity Issues

If you have issues with Netlify Identity, check:

- Email templates
- Registration settings
- Git Gateway settings

### Form Issues

If you have issues with Netlify Forms, check:

- Form markup
- Form submissions in the Netlify dashboard
- Form notifications

## Maintenance

### Updating Dependencies

To update dependencies:

```bash
npm update
```

### Monitoring

Monitor your site using:

- Netlify Analytics
- Google Analytics

### Backups

Regularly back up your site:

1. Clone the repository:

```bash
git clone https://github.com/imKXNNY/USC-Perchtoldsdorf.git
```

2. Back up the `.env` file.
3. Back up any content not stored in the repository.

## Contact

If you have any questions or issues, please contact the website <NAME_EMAIL>.
