/**
 * Site Comparison Tool
 *
 * This script compares content between the original Jimdo site and the new Astro site.
 * It crawls both sites, creates inventories, and generates a comparison report.
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const { parse } = require('csv-parse/sync');
const axios = require('axios');
const cheerio = require('cheerio');

// Configuration
const CONFIG = {
  oldSite: 'https://www.usc-perchtoldsdorf.at',
  newSite: process.env.PUBLIC_SITE_URL || 'https://usc-perchtoldsdorf.netlify.app/',
  outputDir: path.join(__dirname, '../data/site-comparison'),
  maxPages: 100, // Limit for development, set to -1 for unlimited
  screenshotDir: path.join(__dirname, '../data/site-comparison/screenshots'),
};

// Ensure output directories exist
if (!fs.existsSync(CONFIG.outputDir)) {
  fs.mkdirSync(CONFIG.outputDir, { recursive: true });
}
if (!fs.existsSync(CONFIG.screenshotDir)) {
  fs.mkdirSync(CONFIG.screenshotDir, { recursive: true });
}

// Data structures
const oldSiteData = {
  pages: [],
  images: [],
  links: [],
  products: [],
  news: [],
  teams: [],
  sponsors: [],
};

const newSiteData = {
  pages: [],
  images: [],
  links: [],
  products: [],
  news: [],
  teams: [],
  sponsors: [],
};

/**
 * Crawl a website and collect data
 * @param {string} baseUrl - The base URL of the site to crawl
 * @param {Object} dataStore - The data store to populate
 */
async function crawlSite(baseUrl, dataStore) {
  console.log(`Starting crawl of ${baseUrl}`);

  const browser = await puppeteer.launch();
  const page = await browser.newPage();

  // Set viewport for consistent screenshots
  await page.setViewport({ width: 1280, height: 800 });

  // URLs to visit and already visited
  const toVisit = [baseUrl];
  const visited = new Set();

  // Process pages until queue is empty or max pages reached
  while (toVisit.length > 0 && (CONFIG.maxPages === -1 || dataStore.pages.length < CONFIG.maxPages)) {
    const url = toVisit.shift();

    // Skip if already visited
    if (visited.has(url)) continue;
    visited.add(url);

    try {
      // Visit the page
      console.log(`Visiting ${url}`);
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

      // Get page title and metadata
      const title = await page.title();
      const description = await page.$eval('meta[name="description"]', el => el.content).catch(() => '');

      // Take screenshot
      const screenshotPath = path.join(
        CONFIG.screenshotDir,
        `${url.replace(baseUrl, '').replace(/\//g, '_').replace(/[^a-zA-Z0-9_]/g, '') || 'home'}.png`
      );
      await page.screenshot({ path: screenshotPath, fullPage: false });

      // Collect page data
      dataStore.pages.push({
        url,
        title,
        description,
        screenshotPath: path.relative(CONFIG.outputDir, screenshotPath),
      });

      // Collect images
      const images = await page.$$eval('img', imgs => imgs.map(img => ({
        src: img.src,
        alt: img.alt || '',
        width: img.width,
        height: img.height,
      })));

      images.forEach(img => {
        if (img.src && !dataStore.images.some(i => i.src === img.src)) {
          dataStore.images.push({
            src: img.src,
            alt: img.alt,
            width: img.width,
            height: img.height,
            foundOn: url,
          });
        }
      });

      // Collect links and add to visit queue
      const links = await page.$$eval('a', anchors => anchors.map(a => ({
        href: a.href,
        text: a.textContent.trim(),
      })));

      links.forEach(link => {
        // Add to links collection
        if (link.href && !dataStore.links.some(l => l.href === link.href)) {
          dataStore.links.push({
            href: link.href,
            text: link.text,
            foundOn: url,
          });
        }

        // Add to visit queue if it's part of the same site
        if (
          link.href &&
          link.href.startsWith(baseUrl) &&
          !visited.has(link.href) &&
          !toVisit.includes(link.href)
        ) {
          toVisit.push(link.href);
        }
      });

      // Detect page type and collect specific data
      if (url.includes('/shop') || url.includes('/produkt')) {
        await collectProductData(page, url, dataStore);
      } else if (url.includes('/news') || url.includes('/blog')) {
        await collectNewsData(page, url, dataStore);
      } else if (url.includes('/mannschaft') || url.includes('/team')) {
        await collectTeamData(page, url, dataStore);
      } else if (url.includes('/sponsor')) {
        await collectSponsorData(page, url, dataStore);
      }

    } catch (error) {
      console.error(`Error processing ${url}:`, error.message);
    }
  }

  await browser.close();
  console.log(`Completed crawl of ${baseUrl}, found ${dataStore.pages.length} pages`);
}

/**
 * Collect product data from a page
 */
async function collectProductData(page, url, dataStore) {
  try {
    // Extract product information based on site structure
    const productData = await page.evaluate(() => {
      // This will need to be customized based on the actual site structure
      const name = document.querySelector('h1, .product-title, .produkt-title')?.textContent.trim() || '';

      // Try to find price in various formats
      let price = '';
      const priceElement = document.querySelector('.price, .product-price, .produkt-preis');
      if (priceElement) {
        price = priceElement.textContent.trim();
      }

      // Try to extract numeric price
      let numericPrice = 0;
      if (price) {
        // Extract numbers from price string (e.g., "€49.99" -> 49.99)
        const priceMatch = price.match(/[\d.,]+/);
        if (priceMatch) {
          // Replace comma with dot for proper parsing
          numericPrice = parseFloat(priceMatch[0].replace(',', '.'));
        }
      }

      // Try to find description
      const description = document.querySelector('.description, .product-description, .produkt-beschreibung, .j-text')?.textContent.trim() || '';

      // Try to find image
      const imageSrc = document.querySelector('.product-image img, .produkt-bild img, .content img')?.src || '';

      // Try to find category
      let category = 'Fanartikel'; // Default category
      const categoryElement = document.querySelector('.product-category, .produkt-kategorie, .category');
      if (categoryElement) {
        const categoryText = categoryElement.textContent.trim().toLowerCase();
        if (categoryText.includes('trikot')) {
          category = 'Trikots';
        } else if (categoryText.includes('training')) {
          category = 'Trainingskleidung';
        } else if (categoryText.includes('ausrüstung')) {
          category = 'Ausrüstung';
        }
      }

      // Try to find sizes
      const sizes = [];
      const sizeElements = document.querySelectorAll('.sizes option, .size-option, .größe-option');
      sizeElements.forEach(el => {
        const size = el.textContent.trim();
        if (size && !sizes.includes(size)) {
          sizes.push(size);
        }
      });

      // Try to find colors
      const colors = [];
      const colorElements = document.querySelectorAll('.colors option, .color-option, .farbe-option');
      colorElements.forEach(el => {
        const color = el.textContent.trim();
        if (color && !colors.includes(color)) {
          colors.push(color);
        }
      });

      return {
        name,
        price,
        numericPrice,
        description,
        imageSrc,
        category,
        sizes,
        colors
      };
    });

    if (productData.name) {
      dataStore.products.push({
        url,
        name: productData.name,
        price: productData.price,
        numericPrice: productData.numericPrice,
        description: productData.description,
        imageSrc: productData.imageSrc,
        category: productData.category,
        sizes: productData.sizes,
        colors: productData.colors
      });
    }
  } catch (error) {
    console.error(`Error collecting product data from ${url}:`, error.message);
  }
}

/**
 * Collect news data from a page
 */
async function collectNewsData(page, url, dataStore) {
  try {
    // Extract news information based on site structure
    const newsData = await page.evaluate(() => {
      // This will need to be customized based on the actual site structure
      const title = document.querySelector('h1, .news-title, .article-title')?.textContent.trim() || '';

      // Try to find date in various formats
      let dateText = '';
      const dateElement = document.querySelector('.date, .news-date, .article-date, time');
      if (dateElement) {
        dateText = dateElement.textContent.trim() || dateElement.getAttribute('datetime') || '';
      }

      // Try to find content
      const content = document.querySelector('.news-content, .article-content, .content, .j-text')?.textContent.trim() || '';

      // Try to find image
      const imageSrc = document.querySelector('.news-image img, .article-image img, .content img')?.src || '';

      return { title, dateText, content, imageSrc };
    });

    if (newsData.title) {
      // Try to parse date
      let date = new Date();
      if (newsData.dateText) {
        try {
          // Try different date formats
          date = new Date(newsData.dateText);
        } catch (error) {
          console.error(`Error parsing date: ${newsData.dateText}`, error.message);
        }
      }

      dataStore.news.push({
        url,
        title: newsData.title,
        date: date.toISOString(),
        content: newsData.content,
        imageSrc: newsData.imageSrc,
      });
    }
  } catch (error) {
    console.error(`Error collecting news data from ${url}:`, error.message);
  }
}

/**
 * Collect team data from a page
 */
async function collectTeamData(page, url, dataStore) {
  try {
    // Extract team information based on site structure
    const teamData = await page.evaluate(() => {
      // This will need to be customized based on the actual site structure
      const title = document.querySelector('h1, .team-title, .mannschaft-title')?.textContent.trim() || '';

      // Try to find category
      let category = 'Nachwuchs'; // Default category
      const categoryElement = document.querySelector('.team-category, .mannschaft-category, .category');
      if (categoryElement) {
        const categoryText = categoryElement.textContent.trim().toLowerCase();
        if (categoryText.includes('kampfmannschaft')) {
          category = 'Kampfmannschaft';
        } else if (categoryText.includes('u23')) {
          category = 'U23';
        } else if (categoryText.includes('special')) {
          category = 'Special Kickers';
        }
      }

      // Try to find coach
      const coach = document.querySelector('.coach, .trainer')?.textContent.trim() || 'Trainer';

      // Try to find training times
      const trainingTimes = document.querySelector('.training-times, .trainingszeiten')?.textContent.trim() || 'Bitte kontaktieren Sie uns für aktuelle Trainingszeiten';

      // Try to find image
      const imageSrc = document.querySelector('.team-image img, .mannschaft-image img, .content img')?.src || '';

      return { title, category, coach, trainingTimes, imageSrc };
    });

    if (teamData.title) {
      dataStore.teams.push({
        url,
        title: teamData.title,
        category: teamData.category,
        coach: teamData.coach,
        trainingTimes: teamData.trainingTimes,
        imageSrc: teamData.imageSrc,
      });
    }
  } catch (error) {
    console.error(`Error collecting team data from ${url}:`, error.message);
  }
}

/**
 * Collect sponsor data from a page
 */
async function collectSponsorData(page, url, dataStore) {
  try {
    // Extract sponsor information based on site structure
    const sponsorData = await page.evaluate(() => {
      // This will need to be customized based on the actual site structure
      const title = document.querySelector('h1, .sponsor-title')?.textContent.trim() || '';

      // Try to find website
      let website = '';
      const websiteElement = document.querySelector('.sponsor-website a, .website a');
      if (websiteElement) {
        website = websiteElement.href || '';
      }

      // Try to find category
      let category = 'Partner'; // Default category
      const categoryElement = document.querySelector('.sponsor-category, .category');
      if (categoryElement) {
        const categoryText = categoryElement.textContent.trim().toLowerCase();
        if (categoryText.includes('haupt')) {
          category = 'Hauptsponsor';
        } else if (categoryText.includes('premium')) {
          category = 'Premium Partner';
        } else if (categoryText.includes('unterstützer')) {
          category = 'Unterstützer';
        }
      }

      // Try to find logo
      const logoSrc = document.querySelector('.sponsor-logo img, .logo img, .content img')?.src || '';

      return { title, website, category, logoSrc };
    });

    if (sponsorData.title) {
      dataStore.sponsors.push({
        url,
        title: sponsorData.title,
        website: sponsorData.website,
        category: sponsorData.category,
        logoSrc: sponsorData.logoSrc,
      });
    }
  } catch (error) {
    console.error(`Error collecting sponsor data from ${url}:`, error.message);
  }
}

/**
 * Generate comparison report
 */
function generateComparisonReport(oldSiteData, newSiteData) {
  console.log('Generating comparison report...');

  const report = {
    summary: {
      oldSite: {
        pageCount: oldSiteData.pages.length,
        imageCount: oldSiteData.images.length,
        productCount: oldSiteData.products.length,
        newsCount: oldSiteData.news.length,
        teamCount: oldSiteData.teams.length,
        sponsorCount: oldSiteData.sponsors.length,
      },
      newSite: {
        pageCount: newSiteData.pages.length,
        imageCount: newSiteData.images.length,
        productCount: newSiteData.products.length,
        newsCount: newSiteData.news.length,
        teamCount: newSiteData.teams.length,
        sponsorCount: newSiteData.sponsors.length,
      },
      completionPercentage: {
        pages: calculateCompletionPercentage(oldSiteData.pages.length, newSiteData.pages.length),
        products: calculateCompletionPercentage(oldSiteData.products.length, newSiteData.products.length),
        news: calculateCompletionPercentage(oldSiteData.news.length, newSiteData.news.length),
        teams: calculateCompletionPercentage(oldSiteData.teams.length, newSiteData.teams.length),
        sponsors: calculateCompletionPercentage(oldSiteData.sponsors.length, newSiteData.sponsors.length),
      }
    },
    missingPages: [],
    missingImages: [],
    missingProducts: [],
    missingNews: [],
    missingTeams: [],
    missingSponsors: [],
    brokenLinks: [],
    brokenImages: [],
  };

  // Calculate overall completion percentage
  const totalOldItems =
    oldSiteData.pages.length +
    oldSiteData.products.length +
    oldSiteData.news.length +
    oldSiteData.teams.length +
    oldSiteData.sponsors.length;

  const totalNewItems =
    newSiteData.pages.length +
    newSiteData.products.length +
    newSiteData.news.length +
    newSiteData.teams.length +
    newSiteData.sponsors.length;

  report.summary.completionPercentage.overall = calculateCompletionPercentage(totalOldItems, totalNewItems);

  // Find missing pages
  oldSiteData.pages.forEach(oldPage => {
    const relativePath = oldPage.url.replace(CONFIG.oldSite, '');
    const newPageUrl = CONFIG.newSite + relativePath;
    const found = newSiteData.pages.some(p => p.url === newPageUrl);

    if (!found) {
      report.missingPages.push({
        oldUrl: oldPage.url,
        expectedNewUrl: newPageUrl,
        title: oldPage.title,
        priority: getPriority(oldPage.url),
      });
    }
  });

  // Find missing products
  oldSiteData.products.forEach(oldProduct => {
    const found = newSiteData.products.some(p => p.name === oldProduct.name);
    if (!found) {
      report.missingProducts.push({
        ...oldProduct,
        priority: 'high', // Products are typically high priority
      });
    }
  });

  // Find missing news
  oldSiteData.news.forEach(oldNews => {
    const found = newSiteData.news.some(n => n.title === oldNews.title);
    if (!found) {
      report.missingNews.push({
        ...oldNews,
        priority: 'medium', // News are typically medium priority
      });
    }
  });

  // Find missing teams
  oldSiteData.teams.forEach(oldTeam => {
    const found = newSiteData.teams.some(t => t.title === oldTeam.title);
    if (!found) {
      report.missingTeams.push({
        ...oldTeam,
        priority: 'high', // Teams are typically high priority
      });
    }
  });

  // Find missing sponsors
  oldSiteData.sponsors.forEach(oldSponsor => {
    const found = newSiteData.sponsors.some(s => s.title === oldSponsor.title);
    if (!found) {
      report.missingSponsors.push({
        ...oldSponsor,
        priority: 'high', // Sponsors are typically high priority
      });
    }
  });

  // Find broken images
  newSiteData.images.forEach(image => {
    if (!image.src || image.src.includes('placeholder') || image.src.includes('missing')) {
      report.brokenImages.push({
        src: image.src,
        alt: image.alt,
        foundOn: image.foundOn,
        priority: 'medium',
      });
    }
  });

  // Find broken links
  newSiteData.links.forEach(link => {
    if (link.href.startsWith(CONFIG.newSite)) {
      const found = newSiteData.pages.some(p => p.url === link.href);
      if (!found) {
        report.brokenLinks.push({
          href: link.href,
          text: link.text,
          foundOn: link.foundOn,
          priority: 'medium',
        });
      }
    }
  });

  // Sort missing items by priority
  report.missingPages.sort((a, b) => prioritySort(a.priority, b.priority));
  report.missingProducts.sort((a, b) => prioritySort(a.priority, b.priority));
  report.missingNews.sort((a, b) => prioritySort(a.priority, b.priority));
  report.missingTeams.sort((a, b) => prioritySort(a.priority, b.priority));
  report.missingSponsors.sort((a, b) => prioritySort(a.priority, b.priority));

  // Save report
  fs.writeFileSync(
    path.join(CONFIG.outputDir, 'comparison-report.json'),
    JSON.stringify(report, null, 2)
  );

  // Generate HTML report
  generateHtmlReport(report);

  console.log('Comparison report generated');
  console.log(`Overall completion: ${report.summary.completionPercentage.overall}%`);
  console.log(`Pages: ${report.summary.completionPercentage.pages}%`);
  console.log(`Products: ${report.summary.completionPercentage.products}%`);
  console.log(`News: ${report.summary.completionPercentage.news}%`);
  console.log(`Teams: ${report.summary.completionPercentage.teams}%`);
  console.log(`Sponsors: ${report.summary.completionPercentage.sponsors}%`);

  return report;
}

/**
 * Calculate completion percentage
 */
function calculateCompletionPercentage(oldCount, newCount) {
  if (oldCount === 0) return 100; // If there were no items in the old site, consider it 100% complete

  // Cap at 100% to avoid showing more than 100% completion
  const percentage = Math.min(Math.round((newCount / oldCount) * 100), 100);
  return percentage;
}

/**
 * Get priority based on URL
 */
function getPriority(url) {
  // Home page is highest priority
  if (url.endsWith('/') || url.endsWith('/index.html')) {
    return 'critical';
  }

  // Contact page is high priority
  if (url.includes('/kontakt') || url.includes('/contact')) {
    return 'high';
  }

  // About page is high priority
  if (url.includes('/ueber-uns') || url.includes('/about')) {
    return 'high';
  }

  // Shop pages are high priority
  if (url.includes('/shop') || url.includes('/produkt')) {
    return 'high';
  }

  // Team pages are high priority
  if (url.includes('/mannschaft') || url.includes('/team')) {
    return 'high';
  }

  // News pages are medium priority
  if (url.includes('/news') || url.includes('/blog')) {
    return 'medium';
  }

  // Default to medium priority
  return 'medium';
}

/**
 * Sort by priority
 */
function prioritySort(a, b) {
  const priorityValues = {
    'critical': 0,
    'high': 1,
    'medium': 2,
    'low': 3
  };

  return priorityValues[a] - priorityValues[b];
}

/**
 * Generate HTML report
 */
function generateHtmlReport(report) {
  console.log('Generating HTML report...');

  // Create HTML content
  let html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Site Comparison Report - USC Perchtoldsdorf</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066b3; /* USC Perchtoldsdorf blue */
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #0066b3;
      padding-bottom: 10px;
    }
    .header h1 {
      margin: 0;
    }
    .timestamp {
      color: #666;
      font-style: italic;
    }
    .summary {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 30px;
    }
    .summary-card {
      flex: 1;
      min-width: 250px;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .summary-card h3 {
      margin-top: 0;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .progress-container {
      width: 100%;
      background-color: #f1f1f1;
      border-radius: 5px;
      margin: 10px 0;
    }
    .progress-bar {
      height: 20px;
      border-radius: 5px;
      text-align: center;
      line-height: 20px;
      color: white;
      font-weight: bold;
    }
    .progress-high {
      background-color: #4CAF50; /* Green */
    }
    .progress-medium {
      background-color: #2196F3; /* Blue */
    }
    .progress-low {
      background-color: #ff9800; /* Orange */
    }
    .progress-critical {
      background-color: #f44336; /* Red */
    }
    .stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    .missing-items {
      margin-bottom: 30px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .priority-critical {
      background-color: #ffebee; /* Light red */
    }
    .priority-high {
      background-color: #fff8e1; /* Light amber */
    }
    .priority-medium {
      background-color: #e3f2fd; /* Light blue */
    }
    .priority-low {
      background-color: #f1f8e9; /* Light green */
    }
    .tab {
      overflow: hidden;
      border: 1px solid #ccc;
      background-color: #f1f1f1;
      border-radius: 5px 5px 0 0;
    }
    .tab button {
      background-color: inherit;
      float: left;
      border: none;
      outline: none;
      cursor: pointer;
      padding: 14px 16px;
      transition: 0.3s;
      font-size: 16px;
    }
    .tab button:hover {
      background-color: #ddd;
    }
    .tab button.active {
      background-color: #0066b3;
      color: white;
    }
    .tabcontent {
      display: none;
      padding: 20px;
      border: 1px solid #ccc;
      border-top: none;
      border-radius: 0 0 5px 5px;
    }
    .visible {
      display: block;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>USC Perchtoldsdorf - Site Comparison Report</h1>
    <div class="timestamp">Generated on ${new Date().toLocaleString()}</div>
  </div>

  <div class="summary">
    <div class="summary-card">
      <h3>Overall Completion</h3>
      <div class="progress-container">
        <div class="progress-bar ${getProgressClass(report.summary.completionPercentage.overall)}" style="width: ${report.summary.completionPercentage.overall}%">
          ${report.summary.completionPercentage.overall}%
        </div>
      </div>
      <div class="stats">
        <span>Old Site Items:</span>
        <span>${report.summary.oldSite.pageCount + report.summary.oldSite.productCount + report.summary.oldSite.newsCount + report.summary.oldSite.teamCount + report.summary.oldSite.sponsorCount}</span>
      </div>
      <div class="stats">
        <span>New Site Items:</span>
        <span>${report.summary.newSite.pageCount + report.summary.newSite.productCount + report.summary.newSite.newsCount + report.summary.newSite.teamCount + report.summary.newSite.sponsorCount}</span>
      </div>
    </div>

    <div class="summary-card">
      <h3>Pages</h3>
      <div class="progress-container">
        <div class="progress-bar ${getProgressClass(report.summary.completionPercentage.pages)}" style="width: ${report.summary.completionPercentage.pages}%">
          ${report.summary.completionPercentage.pages}%
        </div>
      </div>
      <div class="stats">
        <span>Old Site:</span>
        <span>${report.summary.oldSite.pageCount}</span>
      </div>
      <div class="stats">
        <span>New Site:</span>
        <span>${report.summary.newSite.pageCount}</span>
      </div>
      <div class="stats">
        <span>Missing:</span>
        <span>${report.missingPages.length}</span>
      </div>
    </div>

    <div class="summary-card">
      <h3>Products</h3>
      <div class="progress-container">
        <div class="progress-bar ${getProgressClass(report.summary.completionPercentage.products)}" style="width: ${report.summary.completionPercentage.products}%">
          ${report.summary.completionPercentage.products}%
        </div>
      </div>
      <div class="stats">
        <span>Old Site:</span>
        <span>${report.summary.oldSite.productCount}</span>
      </div>
      <div class="stats">
        <span>New Site:</span>
        <span>${report.summary.newSite.productCount}</span>
      </div>
      <div class="stats">
        <span>Missing:</span>
        <span>${report.missingProducts.length}</span>
      </div>
    </div>

    <div class="summary-card">
      <h3>Teams</h3>
      <div class="progress-container">
        <div class="progress-bar ${getProgressClass(report.summary.completionPercentage.teams)}" style="width: ${report.summary.completionPercentage.teams}%">
          ${report.summary.completionPercentage.teams}%
        </div>
      </div>
      <div class="stats">
        <span>Old Site:</span>
        <span>${report.summary.oldSite.teamCount}</span>
      </div>
      <div class="stats">
        <span>New Site:</span>
        <span>${report.summary.newSite.teamCount}</span>
      </div>
      <div class="stats">
        <span>Missing:</span>
        <span>${report.missingTeams.length}</span>
      </div>
    </div>

    <div class="summary-card">
      <h3>News</h3>
      <div class="progress-container">
        <div class="progress-bar ${getProgressClass(report.summary.completionPercentage.news)}" style="width: ${report.summary.completionPercentage.news}%">
          ${report.summary.completionPercentage.news}%
        </div>
      </div>
      <div class="stats">
        <span>Old Site:</span>
        <span>${report.summary.oldSite.newsCount}</span>
      </div>
      <div class="stats">
        <span>New Site:</span>
        <span>${report.summary.newSite.newsCount}</span>
      </div>
      <div class="stats">
        <span>Missing:</span>
        <span>${report.missingNews.length}</span>
      </div>
    </div>

    <div class="summary-card">
      <h3>Sponsors</h3>
      <div class="progress-container">
        <div class="progress-bar ${getProgressClass(report.summary.completionPercentage.sponsors)}" style="width: ${report.summary.completionPercentage.sponsors}%">
          ${report.summary.completionPercentage.sponsors}%
        </div>
      </div>
      <div class="stats">
        <span>Old Site:</span>
        <span>${report.summary.oldSite.sponsorCount}</span>
      </div>
      <div class="stats">
        <span>New Site:</span>
        <span>${report.summary.newSite.sponsorCount}</span>
      </div>
      <div class="stats">
        <span>Missing:</span>
        <span>${report.missingSponsors.length}</span>
      </div>
    </div>
  </div>

  <div class="tab">
    <button class="tablinks active" onclick="openTab(event, 'MissingPages')">Missing Pages (${report.missingPages.length})</button>
    <button class="tablinks" onclick="openTab(event, 'MissingProducts')">Missing Products (${report.missingProducts.length})</button>
    <button class="tablinks" onclick="openTab(event, 'MissingTeams')">Missing Teams (${report.missingTeams.length})</button>
    <button class="tablinks" onclick="openTab(event, 'MissingNews')">Missing News (${report.missingNews.length})</button>
    <button class="tablinks" onclick="openTab(event, 'MissingSponsors')">Missing Sponsors (${report.missingSponsors.length})</button>
    <button class="tablinks" onclick="openTab(event, 'BrokenLinks')">Broken Links (${report.brokenLinks.length})</button>
    <button class="tablinks" onclick="openTab(event, 'BrokenImages')">Broken Images (${report.brokenImages.length})</button>
  </div>

  <div id="MissingPages" class="tabcontent visible">
    <h2>Missing Pages</h2>
    ${generateMissingPagesTable(report.missingPages)}
  </div>

  <div id="MissingProducts" class="tabcontent">
    <h2>Missing Products</h2>
    ${generateMissingProductsTable(report.missingProducts)}
  </div>

  <div id="MissingTeams" class="tabcontent">
    <h2>Missing Teams</h2>
    ${generateMissingTeamsTable(report.missingTeams)}
  </div>

  <div id="MissingNews" class="tabcontent">
    <h2>Missing News</h2>
    ${generateMissingNewsTable(report.missingNews)}
  </div>

  <div id="MissingSponsors" class="tabcontent">
    <h2>Missing Sponsors</h2>
    ${generateMissingSponsorsTable(report.missingSponsors)}
  </div>

  <div id="BrokenLinks" class="tabcontent">
    <h2>Broken Links</h2>
    ${generateBrokenLinksTable(report.brokenLinks)}
  </div>

  <div id="BrokenImages" class="tabcontent">
    <h2>Broken Images</h2>
    ${generateBrokenImagesTable(report.brokenImages)}
  </div>

  <script>
    function openTab(evt, tabName) {
      var i, tabcontent, tablinks;

      // Hide all tab content
      tabcontent = document.getElementsByClassName("tabcontent");
      for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].classList.remove("visible");
      }

      // Remove active class from all tab buttons
      tablinks = document.getElementsByClassName("tablinks");
      for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
      }

      // Show the current tab and add active class to the button
      document.getElementById(tabName).classList.add("visible");
      evt.currentTarget.className += " active";
    }
  </script>
</body>
</html>
  `;

  // Write HTML file
  fs.writeFileSync(
    path.join(CONFIG.outputDir, 'comparison-report.html'),
    html
  );

  console.log('HTML report generated');
}

/**
 * Get progress bar class based on percentage
 */
function getProgressClass(percentage) {
  if (percentage < 25) return 'progress-critical';
  if (percentage < 50) return 'progress-low';
  if (percentage < 75) return 'progress-medium';
  return 'progress-high';
}

/**
 * Generate missing pages table
 */
function generateMissingPagesTable(missingPages) {
  if (missingPages.length === 0) {
    return '<p>No missing pages found.</p>';
  }

  let table = `
    <table>
      <thead>
        <tr>
          <th>Title</th>
          <th>Old URL</th>
          <th>Expected New URL</th>
          <th>Priority</th>
        </tr>
      </thead>
      <tbody>
  `;

  missingPages.forEach(page => {
    table += `
      <tr class="priority-${page.priority}">
        <td>${page.title}</td>
        <td><a href="${page.oldUrl}" target="_blank">${page.oldUrl}</a></td>
        <td>${page.expectedNewUrl}</td>
        <td>${page.priority}</td>
      </tr>
    `;
  });

  table += `
      </tbody>
    </table>
  `;

  return table;
}

/**
 * Generate missing products table
 */
function generateMissingProductsTable(missingProducts) {
  if (missingProducts.length === 0) {
    return '<p>No missing products found.</p>';
  }

  let table = `
    <table>
      <thead>
        <tr>
          <th>Name</th>
          <th>Price</th>
          <th>Category</th>
          <th>Priority</th>
        </tr>
      </thead>
      <tbody>
  `;

  missingProducts.forEach(product => {
    table += `
      <tr class="priority-${product.priority}">
        <td>${product.name}</td>
        <td>${product.price}</td>
        <td>${product.category || 'N/A'}</td>
        <td>${product.priority}</td>
      </tr>
    `;
  });

  table += `
      </tbody>
    </table>
  `;

  return table;
}

/**
 * Generate missing teams table
 */
function generateMissingTeamsTable(missingTeams) {
  if (missingTeams.length === 0) {
    return '<p>No missing teams found.</p>';
  }

  let table = `
    <table>
      <thead>
        <tr>
          <th>Name</th>
          <th>Category</th>
          <th>Coach</th>
          <th>Priority</th>
        </tr>
      </thead>
      <tbody>
  `;

  missingTeams.forEach(team => {
    table += `
      <tr class="priority-${team.priority}">
        <td>${team.title}</td>
        <td>${team.category || 'N/A'}</td>
        <td>${team.coach || 'N/A'}</td>
        <td>${team.priority}</td>
      </tr>
    `;
  });

  table += `
      </tbody>
    </table>
  `;

  return table;
}

/**
 * Generate missing news table
 */
function generateMissingNewsTable(missingNews) {
  if (missingNews.length === 0) {
    return '<p>No missing news found.</p>';
  }

  let table = `
    <table>
      <thead>
        <tr>
          <th>Title</th>
          <th>Date</th>
          <th>Priority</th>
        </tr>
      </thead>
      <tbody>
  `;

  missingNews.forEach(news => {
    table += `
      <tr class="priority-${news.priority}">
        <td>${news.title}</td>
        <td>${news.date ? new Date(news.date).toLocaleDateString() : 'N/A'}</td>
        <td>${news.priority}</td>
      </tr>
    `;
  });

  table += `
      </tbody>
    </table>
  `;

  return table;
}

/**
 * Generate missing sponsors table
 */
function generateMissingSponsorsTable(missingSponsors) {
  if (missingSponsors.length === 0) {
    return '<p>No missing sponsors found.</p>';
  }

  let table = `
    <table>
      <thead>
        <tr>
          <th>Name</th>
          <th>Category</th>
          <th>Website</th>
          <th>Priority</th>
        </tr>
      </thead>
      <tbody>
  `;

  missingSponsors.forEach(sponsor => {
    table += `
      <tr class="priority-${sponsor.priority}">
        <td>${sponsor.title}</td>
        <td>${sponsor.category || 'N/A'}</td>
        <td>${sponsor.website ? `<a href="${sponsor.website}" target="_blank">${sponsor.website}</a>` : 'N/A'}</td>
        <td>${sponsor.priority}</td>
      </tr>
    `;
  });

  table += `
      </tbody>
    </table>
  `;

  return table;
}

/**
 * Generate broken links table
 */
function generateBrokenLinksTable(brokenLinks) {
  if (brokenLinks.length === 0) {
    return '<p>No broken links found.</p>';
  }

  let table = `
    <table>
      <thead>
        <tr>
          <th>Link Text</th>
          <th>URL</th>
          <th>Found On</th>
          <th>Priority</th>
        </tr>
      </thead>
      <tbody>
  `;

  brokenLinks.forEach(link => {
    table += `
      <tr class="priority-${link.priority}">
        <td>${link.text}</td>
        <td>${link.href}</td>
        <td><a href="${link.foundOn}" target="_blank">${link.foundOn}</a></td>
        <td>${link.priority}</td>
      </tr>
    `;
  });

  table += `
      </tbody>
    </table>
  `;

  return table;
}

/**
 * Generate broken images table
 */
function generateBrokenImagesTable(brokenImages) {
  if (brokenImages.length === 0) {
    return '<p>No broken images found.</p>';
  }

  let table = `
    <table>
      <thead>
        <tr>
          <th>Image Alt</th>
          <th>Image Source</th>
          <th>Found On</th>
          <th>Priority</th>
        </tr>
      </thead>
      <tbody>
  `;

  brokenImages.forEach(image => {
    table += `
      <tr class="priority-${image.priority}">
        <td>${image.alt || 'No alt text'}</td>
        <td>${image.src}</td>
        <td><a href="${image.foundOn}" target="_blank">${image.foundOn}</a></td>
        <td>${image.priority}</td>
      </tr>
    `;
  });

  table += `
      </tbody>
    </table>
  `;

  return table;
}

/**
 * Main function
 */
async function main() {
  try {
    // Crawl both sites
    await crawlSite(CONFIG.oldSite, oldSiteData);
    await crawlSite(CONFIG.newSite, newSiteData);

    // Save raw data
    fs.writeFileSync(
      path.join(CONFIG.outputDir, 'old-site-data.json'),
      JSON.stringify(oldSiteData, null, 2)
    );
    fs.writeFileSync(
      path.join(CONFIG.outputDir, 'new-site-data.json'),
      JSON.stringify(newSiteData, null, 2)
    );

    // Generate comparison report
    const report = generateComparisonReport(oldSiteData, newSiteData);

    console.log('Site comparison completed successfully');
    console.log(`Found ${report.missingPages.length} missing pages`);
    console.log(`Found ${report.missingProducts.length} missing products`);

  } catch (error) {
    console.error('Error in site comparison:', error);
  }
}

// Run the script
main().catch(console.error);
