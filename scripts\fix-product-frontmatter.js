/**
 * <PERSON><PERSON><PERSON> to fix the YAML frontmatter in product markdown files
 */

const fs = require('fs');
const path = require('path');

const productsDir = path.join(__dirname, '..', 'src', 'content', 'products');

// Get all markdown files in the products directory
const productFiles = fs.readdirSync(productsDir)
  .filter(file => file.endsWith('.md'));

console.log(`Found ${productFiles.length} product files to process`);

// Process each file
productFiles.forEach(file => {
  const filePath = path.join(productsDir, file);
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if the file has the problematic frontmatter format
  if (content.startsWith('---\n\n  "')) {
    console.log(`Fixing frontmatter in ${file}`);
    
    // Extract the JSON part between the --- markers
    const match = content.match(/---\n\n([\s\S]*?)\n\n---/);
    
    if (match && match[1]) {
      try {
        // Parse the JSON data
        const jsonData = JSON.parse(`{${match[1]}}`);
        
        // Create new YAML frontmatter
        let newFrontmatter = '---\n';
        
        // Add each property
        for (const [key, value] of Object.entries(jsonData)) {
          if (Array.isArray(value)) {
            newFrontmatter += `${key}:\n`;
            value.forEach(item => {
              newFrontmatter += `  - "${item}"\n`;
            });
          } else if (value === null) {
            newFrontmatter += `${key}: 0\n`;
          } else if (typeof value === 'string') {
            newFrontmatter += `${key}: "${value}"\n`;
          } else {
            newFrontmatter += `${key}: ${value}\n`;
          }
        }
        
        newFrontmatter += '---';
        
        // Replace the old frontmatter with the new one
        const newContent = content.replace(/---\n\n[\s\S]*?\n\n---/, newFrontmatter);
        
        // Write the updated content back to the file
        fs.writeFileSync(filePath, newContent, 'utf8');
        console.log(`✅ Fixed ${file}`);
      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error);
      }
    }
  } else {
    console.log(`✓ ${file} already has correct frontmatter format`);
  }
});

console.log('Done!');
