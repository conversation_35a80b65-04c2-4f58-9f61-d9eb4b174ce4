---
title: "USC Perchtoldsdorf Content-Editor-Anleitung"
description: "Hier findest du eine detaillierte Anleitung, um die Inhalte des USC Perchtoldsdorf-CMS zu bearbeiten."
category: "Admin/Editor"
order: 2
---

# USC Perchtoldsdorf Content-Editor-Anleitung

Diese Anleitung richtet sich an Redakteur\:innen des Vereins und erläutert die wichtigsten Funktionen des Decap CMS in deutscher Sprache. Technische Begriffe bleiben auf Englisch, werden aber kurz erklärt.

## Inhalt der Anleitung

1. **Zugriff auf das CMS** – Wie man die Redaktionsoberfläche erreicht und sich anmeldet.
2. **Authentifizierung** – Anmeldung über Netlify Identity und Passwort-Wiederherstellung.
3. **Dashboard-Überblick** – Kurze Einführung in Statistiken und Schnellzugriffe.
4. **Content-Typen** – Erklärung der verschiedenen Bereiche wie News, Teams, Sponsoren und Produkte.
5. **Medienverwaltung** – Hochladen und Organisieren von Bildern und Dateien.
6. **E-Mail-Integration** – Nutzung der Kontakt- und Newsletter-Formulare.
7. **Best Practices** – Tipps für konsistente Inhalte und Suchmaschinen-Optimierung.
8. **Problemlösung** – Häufige Fehler und deren Behebung.

---

Der nachfolgende Teil des Dokuments ist komplett ins Deutsche übersetzt.

## Inhaltsverzeichnis

1. [Zugriff auf das CMS](#zugriff-auf-das-cms)
2. [Authentifizierung](#authentifizierung)
3. [Dashboard-Überblick](#dashboard-überblick)
4. [Content-Typen](#content-typen)

   * [News](#news)
   * [Teams](#teams)
   * [Sponsoren](#sponsoren)
   * [Produkte](#produkte)
   * [Events](#events)
   * [Seiten](#seiten)
   * [Einstellungen](#einstellungen)
5. [Medienverwaltung](#medienverwaltung)
6. [E-Mail-Integration](#e-mail-integration)
7. [Best Practices](#best-practices)
8. [Problemlösung](#problemlosung)

## Zugriff auf das CMS

Das Content-Management-System (CMS) ist erreichbar unter:

* Produktion: `${PUBLIC_SITE_URL}/admin/cms`
* Lokale Entwicklung: `http://localhost:3000/admin/cms`

Zudem ist das Admin-Dashboard erreichbar unter:

* Produktion: `${PUBLIC_SITE_URL}/admin/dashboard`
* Lokale Entwicklung: `http://localhost:3000/admin/dashboard`

Die Haupt-Admin-Seite unter `/admin` bietet Navigationsoptionen sowohl zum Dashboard als auch zum CMS.

## Authentifizierung

Die Website verwendet Netlify Identity für die Anmeldung. Du musst von einem Administrator eingeladen werden, um Zugriff zu erhalten.

1. **Erstmalige Anmeldung**:

   * Du erhältst eine Einladung per E-Mail von Netlify
   * Klicke in der E-Mail auf den Button „Accept the invite“
   * Du wirst zur Seite weitergeleitet, auf der du dein Passwort festlegen kannst
   * Nach dem Setzen des Passworts wirst du automatisch eingeloggt

2. **Reguläre Anmeldung**:

   * Gehe zur Admin-URL
   * Klicke auf den Button „Login“
   * Gib deine E-Mail-Adresse und dein Passwort ein
   * Klicke auf „Login“

3. **Passwort-Wiederherstellung**:

   * Wenn du dein Passwort vergessen hast, klicke auf „Forgot password?“ im Login-Bildschirm
   * Gib deine E-Mail-Adresse ein
   * Du erhältst per E-Mail einen Link zum Zurücksetzen des Passworts

## Dashboard-Überblick

Das Admin-Dashboard bietet einen Überblick über alle Inhalte der Website:

* **Statistik-Karten**: Zeigen die Anzahl der Einträge in jeder Content-Sammlung
* **Letzte Bestellungen**: Zeigt die jüngsten Shop-Bestellungen (sofern vorhanden)
* **Schnellzugriffe**: Direkter Zugriff auf verschiedene Inhaltsbereiche
* **Aktionen**: Häufige Aktionen wie das Erstellen neuer Inhalte oder Import/Export von Daten

## Content-Typen

### News

News-Artikel erscheinen auf der News-Seite und gegebenenfalls auf der Startseite.

**Felder**:

* **Title**: Überschrift des News-Artikels
* **Publication Date**: Veröffentlichungsdatum
* **Image**: Titelbild für den Artikel
* **Short Description**: Kurze Zusammenfassung für Übersichtsseiten
* **Content**: Hauptinhalt des Artikels (unterstützt Markdown)
* **Tags**: Schlagwörter zur Kategorisierung
* **Author**: Name des Autors

**Best Practices**:

* Klare, prägnante Titel verwenden
* Immer ein Titelbild hinzufügen
* Kurzbeschreibung unter 160 Zeichen halten
* Tags konsistent nutzen

### Teams

Teams repräsentieren die unterschiedlichen Mannschaften des Vereins.

**Felder**:

* **Name**: Teamname
* **Category**: Kategorie (Kampfmannschaft, U23, Nachwuchs, Special Kickers)
* **Image**: Teamfoto
* **Coach**: Name des Cheftrainers
* **Assistant Coach**: Name des Co-Trainers (optional)
* **Training Times**: Trainingszeiten
* **Description**: Informationen zum Team
* **Players**: Liste der Spieler mit Details

**Best Practices**:

* Spielerinformationen stets aktuell halten
* Einheitliches Format für Trainingszeiten verwenden
* Hochwertige Teamfotos nutzen

### Sponsoren

Sponsoren sind Organisationen, die den Verein unterstützen.

**Felder**:

* **Name**: Sponsorenname
* **Logo**: Sponsor-Logo
* **Website**: Link zur Sponsorenseite
* **Category**: Sponsoren-Level
* **Description**: Informationen zum Sponsor

**Best Practices**:

* Hochwertige, transparente Logos verwenden
* Website-Links stets mit „https\://“ versehen
* Sponsoren korrekt nach ihrem Level kategorisieren

### Produkte

Produkte sind Artikel, die im Vereins-Shop erhältlich sind.

**Felder**:

* **Name**: Produktname
* **Price**: Produktpreis
* **Image**: Produktbild
* **Category**: Produktkategorie
* **Description**: Produktdetails
* **Available**: Verfügbarkeit (auf Lager ja/nein)
* **Sizes**: Verfügbare Größen (falls zutreffend)
* **Colors**: Verfügbare Farben (falls zutreffend)

**Best Practices**:

* Mehrere Bilder aus verschiedenen Blickwinkeln nutzen
* Detaillierte Beschreibungen mit Materialien, Pflegehinweisen etc. hinzufügen
* Lagerbestand aktuell halten

### Events

Events umfassen Spiele, Trainingseinheiten, Feiern und andere Vereinsaktivitäten.

**Felder**:

* **Title**: Event-Name
* **Date**: Startdatum und -uhrzeit
* **End Date**: Enddatum und -uhrzeit (optional)
* **Location**: Veranstaltungsort
* **Image**: Event-Bild (optional)
* **Short Description**: Kurze Zusammenfassung für Übersichten
* **Content**: Ausführliche Informationen zum Event
* **Featured**: Soll das Event hervorgehoben werden?
* **Category**: Event-Typ (Match, Training, Celebration, Other)

**Best Practices**:

* Einheitliche Benennung bei wiederkehrenden Events
* Immer Veranstaltungsort angeben
* Bei Spielen Gegner im Titel nennen
* Wichtige Events als „Featured“ markieren

### Seiten

Seiten sind die statischen Hauptinhalte der Website.

#### Startseite (Home Page)

**Felder**:

* **Title**: Seitentitel
* **Hero Title**: Hauptüberschrift im Hero-Bereich
* **Hero Subtitle**: Unterüberschrift im Hero-Bereich
* **Hero Image**: Hintergrundbild für den Hero-Bereich
* **About Text**: Einführungstext
* **CTA Text**: Text des Call-to-Action-Buttons
* **CTA Subtext**: Text unterhalb des Call-to-Action-Buttons

#### Über uns (About Page)

**Felder**:

* **Title**: Seitentitel
* **Image**: Titelbild
* **History**: Vereinsgeschichte
* **Mission**: Mission Statement des Vereins
* **Club Data**: Gründungsjahr, Farben, Mitgliederzahl, Teams
* **Board**: Liste der Vorstandsmitglieder
* **Sports Management**: Liste des Sportmanagement-Teams

#### Kontaktseite (Contact Page)

**Felder**:

* **Title**: Seitentitel
* **Introduction**: Introtext
* **Address**: Vereinsadresse
* **Phone**: Telefonnummer
* **Email**: E-Mail-Adresse
* **Opening Hours**: Öffnungszeiten
* **Google Maps Link**: Link zur Position in Google Maps
* **Form Enabled**: Kontaktformular aktivieren? (ja/nein)
* **Form Success Message**: Erfolgsmeldung nach Absenden

**Best Practices**:

* Kontaktinformationen stets aktuell halten
* Hochwertige Bilder für Hero-Bereiche verwenden
* Prägnante, ansprechende Texte schreiben

### Einstellungen

Einstellungen steuern globale Elemente der Website.

**Navigation**:

* **Main Menu**: Struktur des Hauptmenüs

  * **Text**: Menüpunkt
  * **URL**: Linkziel
  * **Submenu**: Dropdown-Menüpunkte (optional)

**Footer**:

* **Copyright Text**: Copyright-Hinweis
* **Social Media**: Social-Media-Links

  * **Platform**: Plattform
  * **URL**: Profil-Link
  * **Icon**: Icon-Name
* **Footer Links**: Zusätzliche Links im Footer

  * **Text**: Linktext
  * **URL**: Linkziel

**Best Practices**:

* Navigation einfach und intuitiv gestalten
* Alle Links auf Funktion prüfen
* Einheitliches Format für Social-Media-Links

## Medienverwaltung

Alle Mediendateien werden im Verzeichnis `/uploads` gespeichert und über das CMS verwaltet.

**Unterstützte Dateitypen**:

* Bilder: JPG, PNG, GIF, SVG
* Dokumente: PDF
* Videos: MP4 (Einbettung über externe Dienste wie YouTube)

**Best Practices**:

* Beschreibende Dateinamen verwenden
* Bilder für das Web optimieren (komprimieren)
* Einheitliche Namenskonvention beibehalten:

  * Team-Bilder: `team_[name].[ext]`
  * News-Bilder: `news_[title].[ext]`
  * Produkt-Bilder: `product_[name].[ext]`
  * Sponsor-Logos: `sponsor_[name].[ext]`
  * Logos sollten mindestens 300&nbsp;px breit sein, damit sie in der Sponsorenliste scharf dargestellt werden

## E-Mail-Integration

Die Website bietet E-Mail-Funktionalität für Kontaktformulare und Newsletter-Anmeldungen. Diese werden über **Netlify Forms** abgewickelt, die Formulareingaben direkt per E-Mail versenden.

### Kontaktformular

Das Kontaktformular befindet sich auf der Seite `/kontakt` und ermöglicht es Besucher\:innen, Nachrichten an den Verein zu senden.

**Hauptfunktionen**:

* Formularvalidierung sorgt für vollständige Pflichtfelder
* Spam-Schutz verhindert automatisierte Einreichungen
* Bestätigungsmeldungen nach Absenden
* Alle Eingaben werden an die konfigurierte E-Mail-Adresse gesendet

**Verwaltung**:

* Formularfelder können nicht über das CMS angepasst werden
* Eingaben werden nicht auf der Website gespeichert
* Alle Einreichungen werden direkt per E-Mail zugestellt

### Newsletter-Anmeldung

Newsletter-Formulare sind an mehreren Stellen verfügbar:

* Fußzeile auf allen Seiten
* News-Seite
* Eigenständige Newsletter-Komponente, die auf jeder Seite eingefügt werden kann

**Hauptfunktionen**:

* Einfache E-Mail-Erfassung mit Einwilligung
* Validierung stellt gültige E-Mails sicher
* Bestätigungsmeldungen nach Anmeldung
* Alle Anmeldungen werden an die konfigurierte E-Mail-Adresse gesendet

**Verwaltung**:

* Abonnent\:innen werden nicht auf der Website gespeichert
* Alle Anmeldungen werden direkt per E-Mail zugestellt
* Für eine vollständige Verwaltung sollte eine separate Liste (z. B. Tabellenkalkulation oder E-Mail-Marketing-Tool) verwendet werden

### E-Mail-Vorlagen

Netlify Forms nutzt eine einfache Standard-E-Mail-Vorlage. Vorlagen-Beispiele findest du in der Datei `docs/admin/email-templates.md`, falls du in Zukunft einen dedizierten E-Mail-Dienst einsetzen möchtest.

Weitere Details zur E-Mail-Integration findest du in `docs/admin/email-integration.md`.

## Best Practices

1. **Regelmäßige Updates**:

   * Inhalte aktuell halten durch regelmäßige News- und Event-Updates
   * Team-Informationen zu Saisonbeginn aktualisieren
   * Sponsorendaten jährlich überprüfen

2. **Content-Qualität**:

   * Hochwertige Bilder verwenden
   * Texte sorgfältig Korrektur lesen
   * Einheitlichen Ton und Stil wahren

3. **SEO-Aspekte**:

   * Beschreibende, keywordreiche Titel verwenden
   * Sinnvolle Meta-Beschreibungen schreiben
   * Alt-Texte für Bilder ergänzen

4. **Workflow**:

   * Jeweils nur eine Änderung gleichzeitig durchführen
   * Änderungen vor der Veröffentlichung prüfen (Preview)
   * Für wichtige Änderungen den redaktionellen Workflow nutzen (falls aktiviert)

5. **E-Mail-Management**:

   * Kontaktanfragen zeitnah beantworten
   * Separaten Newsletter-Verteiler pflegen
   * Newsletter regelmäßig, aber nicht zu häufig versenden

## Problemlösung

**Häufige Probleme**:

1. **Kann sich nicht einloggen**:

   * Überprüfe die verwendete E-Mail-Adresse
   * Passwort zurücksetzen versuchen
   * Administrator kontaktieren, falls das Problem weiterhin besteht

2. **Änderungen erscheinen nicht**:

   * Warte einige Minuten auf den Neuaufbau der Seite
   * Browser-Cache leeren
   * Prüfen, ob Änderungen veröffentlicht (nicht nur gespeichert) wurden

3. **Bild-Upload funktioniert nicht**:

   * Stelle sicher, dass das Bild unter 10 MB groß ist
   * Anderes Bildformat ausprobieren
   * Internetverbindung prüfen

4. **Editor lädt langsam**:

   * Anderen Browser verwenden
   * Weitere Tabs und Anwendungen schließen
   * Internetverbindung prüfen

5. **Kontaktformular funktioniert nicht**:

   * Prüfe, ob die Seite auf Netlify bereitgestellt und Formulare aktiviert sind
   * Empfänger-E-Mail-Adresse auf Gültigkeit überprüfen
   * Formular mit anderem Browser testen

6. **Newsletter-Anmeldungen kommen nicht an**:

   * Spam-/Junk-Ordner prüfen
   * Überprüfen, ob das Formular korrekt absendet (Browser-Konsole auf Fehlermeldungen checken)

**Hilfe bekommen**:

Bei Fragen, die nicht in dieser Anleitung beantwortet werden, wende dich bitte an die Website-Administration unter [<EMAIL>](mailto:<EMAIL>).
