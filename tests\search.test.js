/**
 * <PERSON><PERSON> test for search functionality
 *
 * This test verifies that the search page works correctly, including:
 * - Basic search functionality
 * - Search with no results
 */

import { test, expect } from '@playwright/test';

test.describe('Search Functionality', () => {
  test('basic search should work', async ({ page }) => {
    // Navigate directly to the search page with a query
    await page.goto('/search?q=trikot');

    // Wait for search results page to load
    await page.waitForSelector('h1:has-text("Suchergebnisse")');

    // Verify search results are displayed
    expect(await page.isVisible('h1:has-text("Suchergebnisse")')).toBeTruthy();

    // Just check that we're on the search results page with the correct query
    expect(page.url()).toContain('/search?q=trikot');

    // Check if the page content contains the search term
    const pageContent = await page.textContent('main');
    expect(pageContent.toLowerCase()).toContain('such');
  });


  test('search with no results should show appropriate message', async ({ page }) => {
    // Navigate directly to search page with a query that should not match anything
    await page.goto('/search?q=xyznonexistentterm123');

    // Wait for search results to load
    await page.waitForSelector('h1:has-text("Suchergebnisse")');

    // Verify we're on the search page with the correct query
    expect(page.url()).toContain('xyznonexistentterm123');

    // Check if there are any search results sections
    const hasResults = await page.isVisible('h2:has-text("Produkte"), h2:has-text("Neuigkeiten")');
    expect(hasResults).toBeFalsy();
  });
});
